import { AuthError } from "./AuthError.js";
import * as ClientConfigurationErrorCodes from "./ClientConfigurationErrorCodes.js";
export { ClientConfigurationErrorCodes };
export declare const ClientConfigurationErrorMessages: {
    redirect_uri_empty: string;
    claims_request_parsing_error: string;
    authority_uri_insecure: string;
    url_parse_error: string;
    empty_url_error: string;
    empty_input_scopes_error: string;
    invalid_claims: string;
    token_request_empty: string;
    logout_request_empty: string;
    invalid_code_challenge_method: string;
    pkce_params_missing: string;
    invalid_cloud_discovery_metadata: string;
    invalid_authority_metadata: string;
    untrusted_authority: string;
    missing_ssh_jwk: string;
    missing_ssh_kid: string;
    missing_nonce_authentication_header: string;
    invalid_authentication_header: string;
    cannot_set_OIDCOptions: string;
    cannot_allow_platform_broker: string;
    authority_mismatch: string;
};
/**
 * ClientConfigurationErrorMessage class containing string constants used by error codes and messages.
 * @deprecated Use ClientConfigurationErrorCodes instead
 */
export declare const ClientConfigurationErrorMessage: {
    redirectUriNotSet: {
        code: string;
        desc: string;
    };
    claimsRequestParsingError: {
        code: string;
        desc: string;
    };
    authorityUriInsecure: {
        code: string;
        desc: string;
    };
    urlParseError: {
        code: string;
        desc: string;
    };
    urlEmptyError: {
        code: string;
        desc: string;
    };
    emptyScopesError: {
        code: string;
        desc: string;
    };
    invalidClaimsRequest: {
        code: string;
        desc: string;
    };
    tokenRequestEmptyError: {
        code: string;
        desc: string;
    };
    logoutRequestEmptyError: {
        code: string;
        desc: string;
    };
    invalidCodeChallengeMethod: {
        code: string;
        desc: string;
    };
    invalidCodeChallengeParams: {
        code: string;
        desc: string;
    };
    invalidCloudDiscoveryMetadata: {
        code: string;
        desc: string;
    };
    invalidAuthorityMetadata: {
        code: string;
        desc: string;
    };
    untrustedAuthority: {
        code: string;
        desc: string;
    };
    missingSshJwk: {
        code: string;
        desc: string;
    };
    missingSshKid: {
        code: string;
        desc: string;
    };
    missingNonceAuthenticationHeader: {
        code: string;
        desc: string;
    };
    invalidAuthenticationHeader: {
        code: string;
        desc: string;
    };
    cannotSetOIDCOptions: {
        code: string;
        desc: string;
    };
    cannotAllowPlatformBroker: {
        code: string;
        desc: string;
    };
    authorityMismatch: {
        code: string;
        desc: string;
    };
};
/**
 * Error thrown when there is an error in configuration of the MSAL.js library.
 */
export declare class ClientConfigurationError extends AuthError {
    constructor(errorCode: string);
}
export declare function createClientConfigurationError(errorCode: string): ClientConfigurationError;
//# sourceMappingURL=ClientConfigurationError.d.ts.map