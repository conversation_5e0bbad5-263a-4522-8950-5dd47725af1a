{"version": 3, "file": "RedirectClient.mjs", "sources": ["../../src/interaction_client/RedirectClient.ts"], "sourcesContent": [null], "names": ["Authorize.getAuthCodeRequestUrl", "Authorize.getEARForm", "BrowserAuthErrorCodes.timedOut", "BrowserUtils.replaceHash", "BrowserUtils.isInIframe", "BrowserUtils.getHomepage", "ResponseHandler.validateInteractionType", "BrowserUtils.clearHash", "BrowserAuthErrorCodes.noStateInHash", "Authorize.handleResponseEAR", "Authorize.handleResponseCode", "BrowserAuthErrorCodes.emptyNavigateUri", "BrowserUtils.getCurrentUri"], "mappings": ";;;;;;;;;;;;;;;AAAA;;;AAGG;AAiDH,SAAS,iBAAiB,GAAA;IACtB,IACI,OAAO,MAAM,KAAK,WAAW;AAC7B,QAAA,OAAO,MAAM,CAAC,WAAW,KAAK,WAAW;AACzC,QAAA,OAAO,MAAM,CAAC,WAAW,CAAC,gBAAgB,KAAK,UAAU,EAC3D;AACE,QAAA,OAAO,SAAS,CAAC;AACpB,KAAA;IAED,MAAM,iBAAiB,GAAG,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AAC5E,IAAA,MAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM;AACvC,UAAG,iBAAiB,CAAC,CAAC,CAAiC;UACrD,SAAS,CAAC;IAChB,OAAO,UAAU,EAAE,IAAI,CAAC;AAC5B,CAAC;AAEK,MAAO,cAAe,SAAQ,yBAAyB,CAAA;AAGzD,IAAA,WAAA,CACI,MAA4B,EAC5B,WAAgC,EAChC,aAAsB,EACtB,MAAc,EACd,YAA0B,EAC1B,gBAAmC,EACnC,iBAAqC,EACrC,iBAAsC,EACtC,mBAA0C,EAC1C,aAAsB,EAAA;AAEtB,QAAA,KAAK,CACD,MAAM,EACN,WAAW,EACX,aAAa,EACb,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,mBAAmB,EACnB,aAAa,CAChB,CAAC;AACF,QAAA,IAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC;KAC1C;AAED;;;AAGG;IACH,MAAM,YAAY,CAAC,OAAwB,EAAA;AACvC,QAAA,MAAM,YAAY,GAAG,MAAM,WAAW,CAClC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9C,iBAAiB,CAAC,uDAAuD,EACzE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,OAAO,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;QAErC,YAAY,CAAC,cAAc,GAAG,qBAAqB,CAC/C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,oBAAoB,EACzB,OAAO,CAAC,oBAAoB,CAC/B,CAAC;AAEF,QAAA,MAAM,gBAAgB,GAAG,CAAC,KAA0B,KAAI;;YAEpD,IAAI,KAAK,CAAC,SAAS,EAAE;AACjB,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,sEAAsE,CACzE,CAAC;AACF,gBAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;AACxC,gBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,oBAAoB,EAC9B,eAAe,CAAC,QAAQ,CAC3B,CAAC;AACL,aAAA;AACL,SAAC,CAAC;QAEF,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAC/C,OAAO,CAAC,iBAAiB,CAC5B,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAwB,qBAAA,EAAA,iBAAiB,CAAE,CAAA,CAAC,CAAC;;AAEpE,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACjC,kBAAkB,CAAC,UAAU,EAC7B,iBAAiB,EACjB,IAAI,CACP,CAAC;;AAGF,QAAA,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;QAEtD,IAAI;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,KAAK,YAAY,CAAC,GAAG,EAAE;AACpD,gBAAA,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;AAC3C,aAAA;AAAM,iBAAA;gBACH,MAAM,IAAI,CAAC,eAAe,CACtB,YAAY,EACZ,OAAO,CAAC,kBAAkB,CAC7B,CAAC;AACL,aAAA;AACJ,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACxB,gBAAA,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC1C,aAAA;AACD,YAAA,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;AACzD,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;;AAIG;AACH,IAAA,MAAM,eAAe,CACjB,OAAsC,EACtC,kBAAoD,EAAA;AAEpD,QAAA,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC5C,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,KAAK,CAAC,oBAAoB,CAC7B,CAAC;AAEF,QAAA,MAAM,SAAS,GAAG,MAAM,WAAW,CAC/B,iBAAiB,EACjB,iBAAiB,CAAC,iBAAiB,EACnC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;AAEtD,QAAA,MAAM,eAAe,GAAG;AACpB,YAAA,GAAG,OAAO;YACV,aAAa,EAAE,SAAS,CAAC,SAAS;SACrC,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,qBAAqB,CACrC,eAAe,EACf,SAAS,CAAC,QAAQ,CACrB,CAAC;QAEF,IAAI;;AAEA,YAAA,MAAM,UAAU,GAA4B,MAAM,WAAW,CACzD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC;gBACE,sBAAsB;gBACtB,gBAAgB,EAAE,eAAe,CAAC,SAAS;gBAC3C,wBAAwB,EAAE,eAAe,CAAC,iBAAiB;gBAC3D,2BAA2B,EACvB,eAAe,CAAC,oBAAoB;gBACxC,OAAO,EAAE,eAAe,CAAC,OAAO;AACnC,aAAA,CAAC,CAAC;;YAGH,MAAM,WAAW,GAAG,MAAM,WAAW,CACjCA,qBAA+B,EAC/B,iBAAiB,CAAC,cAAc,EAChC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG,IAAI,CAAC,MAAM,EACX,UAAU,CAAC,SAAS,EACpB,eAAe,EACf,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CACzB,CAAC;;YAEF,OAAO,MAAM,IAAI,CAAC,mBAAmB,CACjC,WAAW,EACX,kBAAkB,CACrB,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACxB,gBAAA,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACvC,gBAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAChD,aAAA;AACD,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;AAGG;IACH,MAAM,cAAc,CAChB,OAAsC,EAAA;AAEtC,QAAA,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;;AAE5C,QAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,+CAA+C,EACjE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC;YACE,gBAAgB,EAAE,OAAO,CAAC,SAAS;YACnC,wBAAwB,EAAE,OAAO,CAAC,iBAAiB;YACnD,2BAA2B,EAAE,OAAO,CAAC,oBAAoB;YACzD,OAAO,EAAE,OAAO,CAAC,OAAO;AAC3B,SAAA,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,WAAW,CAC5B,cAAc,EACd,iBAAiB,CAAC,cAAc,EAChC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,EAAE,CAAC;AACJ,QAAA,MAAM,eAAe,GAAG;AACpB,YAAA,GAAG,OAAO;AACV,YAAA,MAAM,EAAE,MAAM;SACjB,CAAC;AACF,QAAA,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;QAE3D,MAAM,IAAI,GAAG,MAAMC,UAAoB,CACnC,QAAQ,EACR,IAAI,CAAC,MAAM,EACX,mBAAmB,EACnB,eAAe,EACf,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,CACzB,CAAC;QACF,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,KAAI;YACzC,UAAU,CAAC,MAAK;gBACZ,MAAM,CACF,sBAAsB,CAClBC,QAA8B,EAC9B,oBAAoB,CACvB,CACJ,CAAC;aACL,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;AACrD,SAAC,CAAC,CAAC;KACN;AAED;;;;;;AAMG;IACH,MAAM,qBAAqB,CACvB,IAAe,GAAA,EAAE,EACjB,OAAsC,EACtC,YAAoB,EACpB,iBAA6C,EAAA;QAE7C,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,KAAK,CAAC,qBAAqB,CAC9B,CAAC;QAEF,IAAI;AACA,YAAA,MAAM,CAAC,YAAY,EAAE,cAAc,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAC3D,IAAI,IAAI,EAAE,CACb,CAAC;YACF,IAAI,CAAC,YAAY,EAAE;;AAEf,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,sGAAsG,CACzG,CAAC;AACF,gBAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;;AAGxC,gBAAA,IAAI,iBAAiB,EAAE,KAAK,cAAc,EAAE;AACxC,oBAAA,iBAAiB,CAAC,KAAK,CAAC,SAAS,GAAG,oBAAoB,CAAC;AAC5D,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,iEAAiE,CACpE,CAAC;AACL,iBAAA;AACD,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;;AAGD,YAAA,MAAM,eAAe,GACjB,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACjC,kBAAkB,CAAC,UAAU,EAC7B,IAAI,CACP,IAAI,SAAS,CAAC,YAAY,CAAC;YAChC,MAAM,yBAAyB,GAC3B,SAAS,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;AACjD,YAAA,MAAM,oBAAoB,GAAG,SAAS,CAAC,iBAAiB,CACpD,MAAM,CAAC,QAAQ,CAAC,IAAI,CACvB,CAAC;YAEF,IACI,yBAAyB,KAAK,oBAAoB;AAClD,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAC5C;;AAEE,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oDAAoD,CACvD,CAAC;gBAEF,IAAI,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;;AAEnC,oBAAAC,WAAwB,CAAC,eAAe,CAAC,CAAC;AAC7C,iBAAA;AAED,gBAAA,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,cAAc,CAC9C,YAAY,EACZ,OAAO,EACP,YAAY,EACZ,sBAAsB,CACzB,CAAC;AAEF,gBAAA,OAAO,gBAAgB,CAAC;AAC3B,aAAA;iBAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;AACpD,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,2DAA2D,CAC9D,CAAC;AACF,gBAAA,OAAO,MAAM,IAAI,CAAC,cAAc,CAC5B,YAAY,EACZ,OAAO,EACP,YAAY,EACZ,sBAAsB,CACzB,CAAC;AACL,aAAA;AAAM,iBAAA,IACH,CAACC,UAAuB,EAAE;AAC1B,gBAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,EAC1C;AACE;;;AAGG;AACH,gBAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACjC,kBAAkB,CAAC,QAAQ,EAC3B,cAAc,EACd,IAAI,CACP,CAAC;AACF,gBAAA,MAAM,iBAAiB,GAAsB;oBACzC,KAAK,EAAE,KAAK,CAAC,qBAAqB;AAClC,oBAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACrD,oBAAA,SAAS,EAAE,IAAI;iBAClB,CAAC;AAEF;;;AAGG;gBACH,IAAI,qBAAqB,GAAY,IAAI,CAAC;AAC1C,gBAAA,IAAI,CAAC,eAAe,IAAI,eAAe,KAAK,MAAM,EAAE;;AAEhD,oBAAA,MAAM,QAAQ,GAAGC,WAAwB,EAAE,CAAC;;AAE5C,oBAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACjC,kBAAkB,CAAC,UAAU,EAC7B,QAAQ,EACR,IAAI,CACP,CAAC;AACF,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4EAA4E,CAC/E,CAAC;oBACF,qBAAqB;wBACjB,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,QAAQ,EACR,iBAAiB,CACpB,CAAC;AACT,iBAAA;AAAM,qBAAA;;oBAEH,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAkC,+BAAA,EAAA,eAAe,CAAE,CAAA,CACtD,CAAC;oBACF,qBAAqB;wBACjB,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,eAAe,EACf,iBAAiB,CACpB,CAAC;AACT,iBAAA;;gBAGD,IAAI,CAAC,qBAAqB,EAAE;AACxB,oBAAA,OAAO,MAAM,IAAI,CAAC,cAAc,CAC5B,YAAY,EACZ,OAAO,EACP,YAAY,EACZ,sBAAsB,CACzB,CAAC;AACL,iBAAA;AACJ,aAAA;AAED,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACvB,gBAAA,CAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACtD,gBAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAChD,aAAA;AACD,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;;AAIG;AACO,IAAA,mBAAmB,CACzB,oBAA4B,EAAA;AAE5B,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;;QAEtD,IAAI,cAAc,GAAG,oBAAoB,CAAC;QAC1C,IAAI,CAAC,cAAc,EAAE;YACjB,IACI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB;gBAC/C,kBAAkB,CAAC,KAAK,EAC1B;AACE,gBAAA,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC3C,aAAA;AAAM,iBAAA;AACH,gBAAA,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;AACzC,aAAA;AACJ,SAAA;QACD,IAAI,QAAQ,GAAG,QAAQ,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;AAEhE,QAAA,IAAI,QAAQ,EAAE;YACV,IAAI;AACA,gBAAAC,uBAAuC,CACnC,QAAQ,EACR,IAAI,CAAC,aAAa,EAClB,eAAe,CAAC,QAAQ,CAC3B,CAAC;AACL,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACxB,oBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAA6C,0CAAA,EAAA,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,YAAY,CAAA,CAAE,CAChF,CAAC;AACL,iBAAA;AACD,gBAAA,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACrB,aAAA;AAED,YAAAC,SAAsB,CAAC,MAAM,CAAC,CAAC;AAC/B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,yDAAyD,CAC5D,CAAC;AACF,YAAA,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AACrC,SAAA;AAED,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CACpD,kBAAkB,CAAC,QAAQ,EAC3B,IAAI,CACP,CAAC;AACF,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAC1B,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CACpE,CAAC;AAEF,QAAA,IAAI,UAAU,EAAE;AACZ,YAAA,QAAQ,GAAG,QAAQ,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;AACxD,YAAA,IAAI,QAAQ,EAAE;AACV,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,+DAA+D,CAClE,CAAC;AACF,gBAAA,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AACjC,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;KACrB;AAED;;;;AAIG;IACO,MAAM,cAAc,CAC1B,YAA+B,EAC/B,OAAsC,EACtC,YAAoB,EACpB,sBAA8C,EAAA;AAE9C,QAAA,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,MAAM,sBAAsB,CAACC,aAAmC,CAAC,CAAC;AACrE,SAAA;QAED,IAAI,YAAY,CAAC,OAAO,EAAE;AACtB,YAAA,MAAM,mBAAmB,GAAG,MAAM,WAAW,CACzC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,iBAAiB,CAAC,+CAA+C,EACjE,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC;gBACE,gBAAgB,EAAE,OAAO,CAAC,SAAS;gBACnC,wBAAwB,EAAE,OAAO,CAAC,iBAAiB;gBACnD,2BAA2B,EAAE,OAAO,CAAC,oBAAoB;gBACzD,OAAO,EAAE,OAAO,CAAC,OAAO;AAC3B,aAAA,CAAC,CAAC;AACH,YAAA,OAAO,WAAW,CACdC,iBAA2B,EAC3B,iBAAiB,CAAC,iBAAiB,EACnC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG,OAAO,EACP,YAAY,EACZ,KAAK,CAAC,oBAAoB,EAC1B,IAAI,CAAC,MAAM,EACX,mBAAmB,EACnB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,oBAAoB,CAC5B,CAAC;AACL,SAAA;AAED,QAAA,MAAM,UAAU,GAAG,MAAM,WAAW,CAChC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;AACnE,QAAA,OAAO,WAAW,CACdC,kBAA4B,EAC5B,iBAAiB,CAAC,kBAAkB,EACpC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CACG,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,KAAK,CAAC,oBAAoB,EAC1B,IAAI,CAAC,MAAM,EACX,UAAU,EACV,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,oBAAoB,CAC5B,CAAC;KACL;AAED;;;;AAIG;AACH,IAAA,MAAM,mBAAmB,CACrB,UAAkB,EAClB,yBAA2D,EAAA;AAE3D,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;;AAElE,QAAA,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAqD,kDAAA,EAAA,UAAU,CAAE,CAAA,CACpE,CAAC;AACF,YAAA,MAAM,iBAAiB,GAAsB;gBACzC,KAAK,EAAE,KAAK,CAAC,oBAAoB;AACjC,gBAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACrD,gBAAA,SAAS,EAAE,KAAK;aACnB,CAAC;YAEF,MAAM,kBAAkB,GACpB,yBAAyB;AACzB,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC;;AAGxC,YAAA,IAAI,OAAO,kBAAkB,KAAK,UAAU,EAAE;AAC1C,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,2EAA2E,CAC9E,CAAC;AACF,gBAAA,MAAM,QAAQ,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;;gBAGhD,IAAI,QAAQ,KAAK,KAAK,EAAE;AACpB,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,0FAA0F,CAC7F,CAAC;oBACF,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,UAAU,EACV,iBAAiB,CACpB,CAAC;oBACF,OAAO;AACV,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,6FAA6F,CAChG,CAAC;oBACF,OAAO;AACV,iBAAA;AACJ,aAAA;AAAM,iBAAA;;AAEH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,wEAAwE,CAC3E,CAAC;gBACF,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,UAAU,EACV,iBAAiB,CACpB,CAAC;gBACF,OAAO;AACV,aAAA;AACJ,SAAA;AAAM,aAAA;;AAEH,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,4DAA4D,CAC/D,CAAC;AACF,YAAA,MAAM,sBAAsB,CACxBC,gBAAsC,CACzC,CAAC;AACL,SAAA;KACJ;AAED;;;;AAIG;IACH,MAAM,MAAM,CAAC,aAAiC,EAAA;AAC1C,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QAC7C,MAAM,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;QACvE,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,KAAK,CAAC,MAAM,CACf,CAAC;QAEF,IAAI;AACA,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,YAAY,EACtB,eAAe,CAAC,QAAQ,EACxB,aAAa,CAChB,CAAC;;AAGF,YAAA,MAAM,IAAI,CAAC,kBAAkB,CACzB,IAAI,CAAC,aAAa,EAClB,kBAAkB,CAAC,OAAO,CAC7B,CAAC;AAEF,YAAA,MAAM,iBAAiB,GAAsB;gBACzC,KAAK,EAAE,KAAK,CAAC,MAAM;AACnB,gBAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB;AACrD,gBAAA,SAAS,EAAE,KAAK;aACnB,CAAC;AAEF,YAAA,MAAM,UAAU,GAAG,MAAM,WAAW,CAChC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,iBAAiB,CAAC,6CAA6C,EAC/D,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,CACrB,CAAC;gBACE,sBAAsB;AACtB,gBAAA,gBAAgB,EAAE,aAAa,IAAI,aAAa,CAAC,SAAS;gBAC1D,2BAA2B,EACvB,aAAa,EAAE,oBAAoB;gBACvC,OAAO,EAAE,CAAC,aAAa,IAAI,aAAa,CAAC,OAAO,KAAK,SAAS;AACjE,aAAA,CAAC,CAAC;YAEH,IAAI,UAAU,CAAC,SAAS,CAAC,YAAY,KAAK,YAAY,CAAC,IAAI,EAAE;gBACzD,IAAI;AACA,oBAAA,UAAU,CAAC,SAAS,CAAC,kBAAkB,CAAC;AAC3C,iBAAA;gBAAC,MAAM;AACJ,oBAAA,IAAI,kBAAkB,CAAC,OAAO,EAAE,aAAa,EAAE;AAC3C,wBAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAC7B,kBAAkB,CAAC,OAAO,EAAE,aAAa,EACzC,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,cAAc,EACxB,eAAe,CAAC,QAAQ,EACxB,kBAAkB,CACrB,CAAC;wBAEF,OAAO;AACV,qBAAA;AACJ,iBAAA;AACJ,aAAA;;YAGD,MAAM,SAAS,GACX,UAAU,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;AAEhD,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,cAAc,EACxB,eAAe,CAAC,QAAQ,EACxB,kBAAkB,CACrB,CAAC;;AAEF,YAAA,IACI,aAAa;AACb,gBAAA,OAAO,aAAa,CAAC,kBAAkB,KAAK,UAAU,EACxD;gBACE,MAAM,QAAQ,GAAG,aAAa,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBAE7D,IAAI,QAAQ,KAAK,KAAK,EAAE;AACpB,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4DAA4D,CAC/D,CAAC;;AAEF,oBAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE,EAAE;wBACjD,IAAI,CAAC,cAAc,CAAC,wBAAwB,CACxC,IAAI,EACJ,gBAAgB,CAAC,OAAO,CAC3B,CAAC;AACL,qBAAA;oBACD,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,SAAS,EACT,iBAAiB,CACpB,CAAC;oBACF,OAAO;AACV,iBAAA;AAAM,qBAAA;;AAEH,oBAAA,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACpD,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,+DAA+D,CAClE,CAAC;AACL,iBAAA;AACJ,aAAA;AAAM,iBAAA;;AAEH,gBAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE,EAAE;oBACjD,IAAI,CAAC,cAAc,CAAC,wBAAwB,CACxC,IAAI,EACJ,gBAAgB,CAAC,OAAO,CAC3B,CAAC;AACL,iBAAA;gBACD,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACxC,SAAS,EACT,iBAAiB,CACpB,CAAC;gBACF,OAAO;AACV,aAAA;AACJ,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACvB,gBAAA,CAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACtD,gBAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAChD,aAAA;AACD,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,cAAc,EACxB,eAAe,CAAC,QAAQ,EACxB,IAAI,EACJ,CAAe,CAClB,CAAC;AACF,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,UAAU,EACpB,eAAe,CAAC,QAAQ,CAC3B,CAAC;AACF,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,UAAU,EACpB,eAAe,CAAC,QAAQ,CAC3B,CAAC;KACL;AAED;;;AAGG;AACO,IAAA,oBAAoB,CAAC,gBAAyB,EAAA;QACpD,MAAM,iBAAiB,GAAG,gBAAgB,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnE,OAAO,SAAS,CAAC,cAAc,CAC3B,iBAAiB,EACjBC,aAA0B,EAAE,CAC/B,CAAC;KACL;AACJ;;;;"}