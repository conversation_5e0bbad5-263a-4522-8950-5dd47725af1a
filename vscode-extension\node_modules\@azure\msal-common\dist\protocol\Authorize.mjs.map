{"version": 3, "file": "Authorize.mjs", "sources": ["../../src/protocol/Authorize.ts"], "sourcesContent": [null], "names": ["RequestParameterBuilder.addClientId", "AADServerParamKeys.CLIENT_ID", "RequestParameterBuilder.addScopes", "RequestParameterBuilder.addRedirectUri", "RequestParameterBuilder.addCorrelationId", "RequestParameterBuilder.addResponseMode", "RequestParameterBuilder.addClientInfo", "RequestParameterBuilder.addPrompt", "RequestParameterBuilder.addDomainHint", "RequestParameterBuilder.addSid", "RequestParameterBuilder.addLoginHint", "RequestParameterBuilder.addCcsOid", "RequestParameterBuilder.addCcsUpn", "RequestParameterBuilder.addNonce", "RequestParameterBuilder.addState", "RequestParameterBuilder.addClaims", "RequestParameterBuilder.addBrokerParameters", "AADServerParamKeys.INSTANCE_AWARE", "RequestParameterBuilder.addInstanceAware", "ClientAuthErrorCodes.authorizationCodeMissingFromServerResponse", "ClientAuthErrorCodes.stateNotFound", "ClientAuthErrorCodes.invalidState", "ClientAuthErrorCodes.stateMismatch"], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;AA2BH;;;;;;;AAOG;AACG,SAAU,qCAAqC,CACjD,WAAwB,EACxB,OAAsC,EACtC,MAAc,EACd,iBAAsC,EAAA;;AAGtC,IAAA,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;AAE5C,IAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;AAE7C,IAAAA,WAAmC,CAC/B,UAAU,EACV,OAAO,CAAC,gBAAgB;AACpB,QAAA,OAAO,CAAC,oBAAoB,GAAGC,SAA4B,CAAC;QAC5D,WAAW,CAAC,QAAQ,CAC3B,CAAC;AAEF,IAAA,MAAM,aAAa,GAAG;AAClB,QAAA,IAAI,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;AACzB,QAAA,IAAI,OAAO,CAAC,oBAAoB,IAAI,EAAE,CAAC;KAC1C,CAAC;AACF,IAAAC,SAAiC,CAC7B,UAAU,EACV,aAAa,EACb,IAAI,EACJ,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,aAAa,CAC3D,CAAC;IAEFC,cAAsC,CAAC,UAAU,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;AAExE,IAAAC,gBAAwC,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;;IAGpEC,eAAuC,CAAC,UAAU,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;;AAG1E,IAAAC,aAAqC,CAAC,UAAU,CAAC,CAAC;IAElD,IAAI,OAAO,CAAC,MAAM,EAAE;QAChBC,SAAiC,CAAC,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9D,QAAA,iBAAiB,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,aAAa,CAAC,CAAC;AAC3E,KAAA;IAED,IAAI,OAAO,CAAC,UAAU,EAAE;QACpBC,aAAqC,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QACtE,iBAAiB,EAAE,SAAS,CACxB,EAAE,qBAAqB,EAAE,IAAI,EAAE,EAC/B,aAAa,CAChB,CAAC;AACL,KAAA;;AAGD,IAAA,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,cAAc,EAAE;;QAE/C,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI,EAAE;;AAEpD,YAAA,MAAM,CAAC,OAAO,CACV,uEAAuE,CAC1E,CAAC;YACFC,MAA8B,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;YACxD,iBAAiB,EAAE,SAAS,CACxB,EAAE,cAAc,EAAE,IAAI,EAAE,EACxB,aAAa,CAChB,CAAC;AACL,SAAA;aAAM,IAAI,OAAO,CAAC,OAAO,EAAE;YACxB,MAAM,UAAU,GAAG,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACtD,IAAI,qBAAqB,GAAG,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAE9D,YAAA,IAAI,qBAAqB,IAAI,OAAO,CAAC,UAAU,EAAE;AAC7C,gBAAA,MAAM,CAAC,OAAO,CACV,CAAA,2JAAA,CAA6J,CAChK,CAAC;gBACF,qBAAqB,GAAG,IAAI,CAAC;AAChC,aAAA;;AAGD,YAAA,IAAI,qBAAqB,EAAE;AACvB,gBAAA,MAAM,CAAC,OAAO,CACV,mEAAmE,CACtE,CAAC;AACF,gBAAAC,YAAoC,CAChC,UAAU,EACV,qBAAqB,CACxB,CAAC;gBACF,iBAAiB,EAAE,SAAS,CACxB,EAAE,kBAAkB,EAAE,IAAI,EAAE,EAC5B,aAAa,CAChB,CAAC;gBACF,IAAI;oBACA,MAAM,UAAU,GAAG,gCAAgC,CAC/C,OAAO,CAAC,OAAO,CAAC,aAAa,CAChC,CAAC;AACF,oBAAAC,SAAiC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC7D,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;AACR,oBAAA,MAAM,CAAC,OAAO,CACV,8EAA8E,CACjF,CAAC;AACL,iBAAA;AACJ,aAAA;iBAAM,IAAI,UAAU,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI,EAAE;AAC1D;;;AAGG;AACH,gBAAA,MAAM,CAAC,OAAO,CACV,uEAAuE,CAC1E,CAAC;AACF,gBAAAF,MAA8B,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;gBACvD,iBAAiB,EAAE,SAAS,CACxB,EAAE,YAAY,EAAE,IAAI,EAAE,EACtB,aAAa,CAChB,CAAC;gBACF,IAAI;oBACA,MAAM,UAAU,GAAG,gCAAgC,CAC/C,OAAO,CAAC,OAAO,CAAC,aAAa,CAChC,CAAC;AACF,oBAAAE,SAAiC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC7D,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;AACR,oBAAA,MAAM,CAAC,OAAO,CACV,8EAA8E,CACjF,CAAC;AACL,iBAAA;AACJ,aAAA;iBAAM,IAAI,OAAO,CAAC,SAAS,EAAE;AAC1B,gBAAA,MAAM,CAAC,OAAO,CACV,8DAA8D,CACjE,CAAC;gBACFD,YAAoC,CAChC,UAAU,EACV,OAAO,CAAC,SAAS,CACpB,CAAC;gBACFE,SAAiC,CAC7B,UAAU,EACV,OAAO,CAAC,SAAS,CACpB,CAAC;gBACF,iBAAiB,EAAE,SAAS,CACxB,EAAE,oBAAoB,EAAE,IAAI,EAAE,EAC9B,aAAa,CAChB,CAAC;AACL,aAAA;AAAM,iBAAA,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE;;AAEjC,gBAAA,MAAM,CAAC,OAAO,CACV,8DAA8D,CACjE,CAAC;gBACFF,YAAoC,CAChC,UAAU,EACV,OAAO,CAAC,OAAO,CAAC,QAAQ,CAC3B,CAAC;gBACF,iBAAiB,EAAE,SAAS,CACxB,EAAE,gBAAgB,EAAE,IAAI,EAAE,EAC1B,aAAa,CAChB,CAAC;gBACF,IAAI;oBACA,MAAM,UAAU,GAAG,gCAAgC,CAC/C,OAAO,CAAC,OAAO,CAAC,aAAa,CAChC,CAAC;AACF,oBAAAC,SAAiC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC7D,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;AACR,oBAAA,MAAM,CAAC,OAAO,CACV,8EAA8E,CACjF,CAAC;AACL,iBAAA;AACJ,aAAA;AACJ,SAAA;aAAM,IAAI,OAAO,CAAC,SAAS,EAAE;AAC1B,YAAA,MAAM,CAAC,OAAO,CACV,0EAA0E,CAC7E,CAAC;YACFD,YAAoC,CAAC,UAAU,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;YACpEE,SAAiC,CAAC,UAAU,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;YACjE,iBAAiB,EAAE,SAAS,CACxB,EAAE,oBAAoB,EAAE,IAAI,EAAE,EAC9B,aAAa,CAChB,CAAC;AACL,SAAA;AACJ,KAAA;AAAM,SAAA;AACH,QAAA,MAAM,CAAC,OAAO,CACV,gFAAgF,CACnF,CAAC;AACL,KAAA;IAED,IAAI,OAAO,CAAC,KAAK,EAAE;QACfC,QAAgC,CAAC,UAAU,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/D,KAAA;IAED,IAAI,OAAO,CAAC,KAAK,EAAE;QACfC,QAAgC,CAAC,UAAU,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/D,KAAA;IAED,IACI,OAAO,CAAC,MAAM;SACb,WAAW,CAAC,kBAAkB;AAC3B,YAAA,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EAChD;AACE,QAAAC,SAAiC,CAC7B,UAAU,EACV,OAAO,CAAC,MAAM,EACd,WAAW,CAAC,kBAAkB,CACjC,CAAC;AACL,KAAA;IAED,IAAI,OAAO,CAAC,gBAAgB,EAAE;AAC1B,QAAAC,mBAA2C,CACvC,UAAU,EACV,WAAW,CAAC,QAAQ,EACpB,WAAW,CAAC,WAAW,CAC1B,CAAC;AACL,KAAA;;IAGD,IACI,WAAW,CAAC,aAAa;SACxB,CAAC,OAAO,CAAC,oBAAoB;AAC1B,YAAA,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,QAAQ,CAC/CC,cAAiC,CACpC,CAAC,EACR;AACE,QAAAC,gBAAwC,CAAC,UAAU,CAAC,CAAC;AACxD,KAAA;AAED,IAAA,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;;AAKG;AACG,SAAU,eAAe,CAC3B,SAAoB,EACpB,iBAAsC,EACtC,YAAsB,EACtB,oBAA6C,EAAA;IAE7C,MAAM,WAAW,GAAG,gBAAgB,CAChC,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,CACvB,CAAC;IACF,OAAO,SAAS,CAAC,iBAAiB,CAC9B,SAAS,CAAC,qBAAqB,EAC/B,WAAW,CACd,CAAC;AACN,CAAC;AAED;;;;;AAKG;AACa,SAAA,2BAA2B,CACvC,YAA+B,EAC/B,WAAmB,EAAA;;AAGnB,IAAA,6BAA6B,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;;AAGzD,IAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AACpB,QAAA,MAAM,qBAAqB,CACvBC,0CAA+D,CAClE,CAAC;AACL,KAAA;AAED,IAAA,OAAO,YAAwC,CAAC;AACpD,CAAC;AAED;;;;AAIG;AACa,SAAA,6BAA6B,CACzC,cAAiC,EACjC,YAAoB,EAAA;AAEpB,IAAA,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,CAAC,YAAY,EAAE;QACxC,MAAM,cAAc,CAAC,KAAK;cACpB,qBAAqB,CACjBC,aAAkC,EAClC,cAAc,CACjB;cACD,qBAAqB,CACjBA,aAAkC,EAClC,cAAc,CACjB,CAAC;AACX,KAAA;AAED,IAAA,IAAI,0BAAkC,CAAC;AACvC,IAAA,IAAI,mBAA2B,CAAC;IAEhC,IAAI;AACA,QAAA,0BAA0B,GAAG,kBAAkB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACzE,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;QACR,MAAM,qBAAqB,CACvBC,YAAiC,EACjC,cAAc,CAAC,KAAK,CACvB,CAAC;AACL,KAAA;IAED,IAAI;AACA,QAAA,mBAAmB,GAAG,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAC1D,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;QACR,MAAM,qBAAqB,CACvBA,YAAiC,EACjC,cAAc,CAAC,KAAK,CACvB,CAAC;AACL,KAAA;IAED,IAAI,0BAA0B,KAAK,mBAAmB,EAAE;AACpD,QAAA,MAAM,qBAAqB,CAACC,aAAkC,CAAC,CAAC;AACnE,KAAA;;IAGD,IACI,cAAc,CAAC,KAAK;AACpB,QAAA,cAAc,CAAC,iBAAiB;QAChC,cAAc,CAAC,QAAQ,EACzB;AACE,QAAA,MAAM,aAAa,GAAG,kBAAkB,CAAC,cAAc,CAAC,CAAC;AACzD,QAAA,IACI,0BAA0B,CACtB,cAAc,CAAC,KAAK,EACpB,cAAc,CAAC,iBAAiB,EAChC,cAAc,CAAC,QAAQ,CAC1B,EACH;AACE,YAAA,MAAM,IAAI,4BAA4B,CAClC,cAAc,CAAC,KAAK,IAAI,EAAE,EAC1B,cAAc,CAAC,iBAAiB,EAChC,cAAc,CAAC,QAAQ,EACvB,cAAc,CAAC,SAAS,IAAI,EAAE,EAC9B,cAAc,CAAC,QAAQ,IAAI,EAAE,EAC7B,cAAc,CAAC,cAAc,IAAI,EAAE,EACnC,cAAc,CAAC,MAAM,IAAI,EAAE,EAC3B,aAAa,CAChB,CAAC;AACL,SAAA;AAED,QAAA,MAAM,IAAI,WAAW,CACjB,cAAc,CAAC,KAAK,IAAI,EAAE,EAC1B,cAAc,CAAC,iBAAiB,EAChC,cAAc,CAAC,QAAQ,EACvB,aAAa,CAChB,CAAC;AACL,KAAA;AACL,CAAC;AAED;;;;AAIG;AACH,SAAS,kBAAkB,CACvB,cAAiC,EAAA;IAEjC,MAAM,eAAe,GAAG,OAAO,CAAC;IAChC,MAAM,oBAAoB,GACtB,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;AAC3D,IAAA,OAAO,oBAAoB,IAAI,oBAAoB,IAAI,CAAC;AACpD,UAAE,cAAc,CAAC,SAAS,EAAE,SAAS,CAC/B,oBAAoB,GAAG,eAAe,CAAC,MAAM,CAChD;UACD,SAAS,CAAC;AACpB,CAAC;AAED;;;AAGG;AACH,SAAS,iBAAiB,CAAC,OAAoB,EAAA;AAC3C,IAAA,OAAO,OAAO,CAAC,aAAa,EAAE,GAAG,IAAI,IAAI,CAAC;AAC9C,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAoB,EAAA;AAC1C,IAAA,OAAO,OAAO,CAAC,aAAa,EAAE,UAAU,IAAI,IAAI,CAAC;AACrD;;;;"}