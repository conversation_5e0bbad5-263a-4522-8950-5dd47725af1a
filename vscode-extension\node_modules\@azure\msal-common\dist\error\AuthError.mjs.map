{"version": 3, "file": "AuthError.mjs", "sources": ["../../src/error/AuthError.ts"], "sourcesContent": [null], "names": ["AuthErrorCodes.unexpectedError", "AuthErrorCodes.postRequestFailed"], "mappings": ";;;;;;;AAAA;;;AAGG;AAMU,MAAA,iBAAiB,GAAG;AAC7B,IAAA,CAACA,eAA8B,GAAG,qCAAqC;AACvE,IAAA,CAACC,iBAAgC,GAC7B,sIAAsI;EAC5I;AAEF;;;AAGG;AACU,MAAA,gBAAgB,GAAG;AAC5B,IAAA,eAAe,EAAE;QACb,IAAI,EAAED,eAA8B;AACpC,QAAA,IAAI,EAAE,iBAAiB,CAACA,eAA8B,CAAC;AAC1D,KAAA;AACD,IAAA,iBAAiB,EAAE;QACf,IAAI,EAAEC,iBAAgC;AACtC,QAAA,IAAI,EAAE,iBAAiB,CAACA,iBAAgC,CAAC;AAC5D,KAAA;EACH;AAEF;;AAEG;AACG,MAAO,SAAU,SAAQ,KAAK,CAAA;AAqBhC,IAAA,WAAA,CAAY,SAAkB,EAAE,YAAqB,EAAE,QAAiB,EAAA;QACpE,MAAM,WAAW,GAAG,YAAY;AAC5B,cAAE,CAAA,EAAG,SAAS,CAAA,EAAA,EAAK,YAAY,CAAE,CAAA;cAC/B,SAAS,CAAC;QAChB,KAAK,CAAC,WAAW,CAAC,CAAC;QACnB,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;QAEjD,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,SAAS,CAAC,YAAY,CAAC;QACrD,IAAI,CAAC,YAAY,GAAG,YAAY,IAAI,SAAS,CAAC,YAAY,CAAC;QAC3D,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,SAAS,CAAC,YAAY,CAAC;AACnD,QAAA,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;KAC3B;AAED,IAAA,gBAAgB,CAAC,aAAqB,EAAA;AAClC,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;KACtC;AACJ,CAAA;AAEe,SAAA,eAAe,CAC3B,IAAY,EACZ,iBAA0B,EAAA;AAE1B,IAAA,OAAO,IAAI,SAAS,CAChB,IAAI,EACJ,iBAAiB;UACX,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAA,CAAA,EAAI,iBAAiB,CAAE,CAAA;AACnD,UAAE,iBAAiB,CAAC,IAAI,CAAC,CAChC,CAAC;AACN;;;;"}