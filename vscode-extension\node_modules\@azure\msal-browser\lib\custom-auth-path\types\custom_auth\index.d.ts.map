{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../../src/custom_auth/index.ts"], "names": [], "mappings": "AAKA;;;GAGG;AAEH;;;GAGG;AAGH,OAAO,EAAE,iCAAiC,EAAE,MAAM,wCAAwC,CAAC;AAC3F,OAAO,EAAE,kCAAkC,EAAE,MAAM,yCAAyC,CAAC;AAG7F,OAAO,EAAE,uBAAuB,EAAE,MAAM,4CAA4C,CAAC;AAGrF,OAAO,EAAE,qBAAqB,EAAE,MAAM,kDAAkD,CAAC;AAGzF,OAAO,EACH,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,iCAAiC,GACpC,MAAM,6BAA6B,CAAC;AAGrC,OAAO,EAAE,iBAAiB,EAAE,MAAM,mCAAmC,CAAC;AACtE,OAAO,EAAE,+BAA+B,EAAE,MAAM,mCAAmC,CAAC;AAGpF,OAAO,EAAE,WAAW,EAAE,MAAM,0CAA0C,CAAC;AACvE,OAAO,EAAE,uBAAuB,EAAE,MAAM,sDAAsD,CAAC;AAC/F,OAAO,EAAE,uBAAuB,EAAE,MAAM,sDAAsD,CAAC;AAC/F,OAAO,EAAE,2BAA2B,EAAE,MAAM,0DAA0D,CAAC;AACvG,OAAO,EAAE,oBAAoB,EAAE,MAAM,mDAAmD,CAAC;AACzF,OAAO,EAAE,iBAAiB,EAAE,MAAM,gDAAgD,CAAC;AAGnF,OAAO,EACH,YAAY,EACZ,iBAAiB,GACpB,MAAM,4CAA4C,CAAC;AACpD,OAAO,EAAE,sBAAsB,EAAE,MAAM,sDAAsD,CAAC;AAC9F,OAAO,EACH,sBAAsB,EACtB,2BAA2B,GAC9B,MAAM,sDAAsD,CAAC;AAC9D,OAAO,EAAE,0BAA0B,EAAE,MAAM,0DAA0D,CAAC;AACtG,OAAO,EAAE,iCAAiC,EAAE,MAAM,4DAA4D,CAAC;AAG/G,OAAO,EACH,WAAW,EACX,yBAAyB,EACzB,qBAAqB,EACrB,qBAAqB,GACxB,MAAM,+CAA+C,CAAC;AAGvD,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAC;AAGnE,OAAO,EAAE,WAAW,EAAE,MAAM,0CAA0C,CAAC;AACvE,OAAO,EAAE,6BAA6B,EAAE,MAAM,4DAA4D,CAAC;AAC3G,OAAO,EAAE,uBAAuB,EAAE,MAAM,sDAAsD,CAAC;AAC/F,OAAO,EAAE,2BAA2B,EAAE,MAAM,0DAA0D,CAAC;AACvG,OAAO,EAAE,oBAAoB,EAAE,MAAM,mDAAmD,CAAC;AACzF,OAAO,EAAE,iBAAiB,EAAE,MAAM,gDAAgD,CAAC;AAGnF,OAAO,EACH,YAAY,EACZ,iBAAiB,GACpB,MAAM,4CAA4C,CAAC;AACpD,OAAO,EACH,4BAA4B,EAC5B,iCAAiC,GACpC,MAAM,4DAA4D,CAAC;AACpE,OAAO,EACH,sBAAsB,EACtB,2BAA2B,GAC9B,MAAM,sDAAsD,CAAC;AAC9D,OAAO,EACH,sBAAsB,EACtB,2BAA2B,GAC9B,MAAM,sDAAsD,CAAC;AAC9D,OAAO,EACH,0BAA0B,EAC1B,+BAA+B,GAClC,MAAM,0DAA0D,CAAC;AAGlE,OAAO,EACH,WAAW,EACX,yBAAyB,EACzB,qBAAqB,EACrB,2BAA2B,EAC3B,qBAAqB,GACxB,MAAM,+CAA+C,CAAC;AAGvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,wDAAwD,CAAC;AAC5F,OAAO,EAAE,8BAA8B,EAAE,MAAM,oEAAoE,CAAC;AACpH,OAAO,EAAE,kCAAkC,EAAE,MAAM,wEAAwE,CAAC;AAC5H,OAAO,EAAE,2BAA2B,EAAE,MAAM,iEAAiE,CAAC;AAC9G,OAAO,EAAE,wBAAwB,EAAE,MAAM,8DAA8D,CAAC;AAGxG,OAAO,EACH,wBAAwB,EACxB,6BAA6B,GAChC,MAAM,+DAA+D,CAAC;AACvE,OAAO,EACH,6BAA6B,EAC7B,kCAAkC,GACrC,MAAM,oEAAoE,CAAC;AAC5E,OAAO,EACH,6BAA6B,EAC7B,kCAAkC,GACrC,MAAM,oEAAoE,CAAC;AAC5E,OAAO,EACH,iCAAiC,EACjC,sCAAsC,GACzC,MAAM,wEAAwE,CAAC;AAGhF,OAAO,EACH,kBAAkB,EAClB,gCAAgC,EAChC,4BAA4B,EAC5B,4BAA4B,GAC/B,MAAM,6DAA6D,CAAC;AAGrE,OAAO,EACH,oBAAoB,EACpB,yBAAyB,GAC5B,MAAM,wDAAwD,CAAC;AAGhE,OAAO,EACH,gBAAgB,EAChB,qBAAqB,GACxB,MAAM,oDAAoD,CAAC;AAG5D,OAAO,EACH,aAAa,EACb,kBAAkB,GACrB,MAAM,iDAAiD,CAAC;AAGzD,OAAO,EACH,eAAe,EACf,YAAY,EACZ,iCAAiC,GACpC,MAAM,uDAAuD,CAAC;AAG/D,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAC;AACxE,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAClE,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAC;AACtD,OAAO,EAAE,oBAAoB,EAAE,MAAM,sCAAsC,CAAC;AAC5E,OAAO,EAAE,yBAAyB,EAAE,MAAM,2CAA2C,CAAC;AACtF,OAAO,EAAE,yBAAyB,EAAE,MAAM,2CAA2C,CAAC;AACtF,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AAC1E,OAAO,EAAE,yBAAyB,EAAE,MAAM,2CAA2C,CAAC;AACtF,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAChE,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAClE,OAAO,EAAE,2BAA2B,EAAE,MAAM,6CAA6C,CAAC;AAC1F,OAAO,EAAE,yBAAyB,EAAE,MAAM,2CAA2C,CAAC;AACtF,OAAO,EAAE,wBAAwB,EAAE,MAAM,0CAA0C,CAAC;AAGpF,OAAO,EAAE,QAAQ,EAAE,MAAM,4BAA4B,CAAC"}