{"version": 3, "file": "ICrypto.mjs", "sources": ["../../src/crypto/ICrypto.ts"], "sourcesContent": [null], "names": ["ClientAuthErrorCodes.methodNotImplemented"], "mappings": ";;;;;AAAA;;;AAGG;AA2FU,MAAA,6BAA6B,GAAY;IAClD,aAAa,EAAE,MAAa;AACxB,QAAA,MAAM,qBAAqB,CAACA,oBAAyC,CAAC,CAAC;KAC1E;IACD,YAAY,EAAE,MAAa;AACvB,QAAA,MAAM,qBAAqB,CAACA,oBAAyC,CAAC,CAAC;KAC1E;IACD,YAAY,EAAE,MAAa;AACvB,QAAA,MAAM,qBAAqB,CAACA,oBAAyC,CAAC,CAAC;KAC1E;IACD,eAAe,EAAE,MAAa;AAC1B,QAAA,MAAM,qBAAqB,CAACA,oBAAyC,CAAC,CAAC;KAC1E;IACD,SAAS,EAAE,MAAa;AACpB,QAAA,MAAM,qBAAqB,CAACA,oBAAyC,CAAC,CAAC;KAC1E;AACD,IAAA,MAAM,sBAAsB,GAAA;AACxB,QAAA,MAAM,qBAAqB,CAACA,oBAAyC,CAAC,CAAC;KAC1E;AACD,IAAA,MAAM,qBAAqB,GAAA;AACvB,QAAA,MAAM,qBAAqB,CAACA,oBAAyC,CAAC,CAAC;KAC1E;AACD,IAAA,MAAM,aAAa,GAAA;AACf,QAAA,MAAM,qBAAqB,CAACA,oBAAyC,CAAC,CAAC;KAC1E;AACD,IAAA,MAAM,OAAO,GAAA;AACT,QAAA,MAAM,qBAAqB,CAACA,oBAAyC,CAAC,CAAC;KAC1E;AACD,IAAA,MAAM,UAAU,GAAA;AACZ,QAAA,MAAM,qBAAqB,CAACA,oBAAyC,CAAC,CAAC;KAC1E;;;;;"}