{"version": 3, "file": "AzureArc.mjs", "sources": ["../../../src/client/ManagedIdentitySources/AzureArc.ts"], "sourcesContent": [null], "names": ["fsConstants", "ManagedIdentityErrorCodes.unableToCreateAzureArc", "ManagedIdentityErrorCodes.wwwAuthenticateHeaderMissing", "ManagedIdentityErrorCodes.wwwAuthenticateHeaderUnsupportedFormat", "ManagedIdentityErrorCodes.platformNotSupported", "ManagedIdentityErrorCodes.invalidFileExtension", "ManagedIdentityErrorCodes.invalidFilePath", "ManagedIdentityErrorCodes.unableToReadSecretFile", "ManagedIdentityErrorCodes.invalidSecret"], "mappings": ";;;;;;;;;;;AAAA;;;AAGG;AAyCI,MAAM,eAAe,GAAW,aAAa;AAC7C,MAAM,mCAAmC,GAC5C,wDAAwD;AAC5D,MAAM,8BAA8B,GAAG,8BAA8B,CAAC;AAOzD,MAAA,6BAA6B,GAAgB;IACtD,KAAK,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAwC,sCAAA,CAAA;AAC5E,IAAA,KAAK,EAAE,4BAA4B;EACrC;AAEW,MAAA,wBAAwB,GAAgB;IACjD,KAAK,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAyC,uCAAA,CAAA;AAC9E,IAAA,KAAK,EAAE,0BAA0B;EACnC;AAEF;;AAEG;AACG,MAAO,QAAS,SAAQ,yBAAyB,CAAA;IAGnD,WACI,CAAA,MAAc,EACd,WAAwB,EACxB,aAA6B,EAC7B,cAA8B,EAC9B,sBAA+B,EAC/B,gBAAwB,EAAA;QAExB,KAAK,CACD,MAAM,EACN,WAAW,EACX,aAAa,EACb,cAAc,EACd,sBAAsB,CACzB,CAAC;AAEF,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;KAC5C;AAEM,IAAA,OAAO,uBAAuB,GAAA;QACjC,IAAI,gBAAgB,GAChB,OAAO,CAAC,GAAG,CACP,uCAAuC,CAAC,iBAAiB,CAC5D,CAAC;QACN,IAAI,YAAY,GACZ,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,aAAa,CAAC,CAAC;;AAGvE,QAAA,IAAI,CAAC,gBAAgB,IAAI,CAAC,YAAY,EAAE;;YAEpC,MAAM,iBAAiB,GACnB,wBAAwB,CAAC,OAAO,CAAC,QAA6B,CAAC,CAAC;YACpE,IAAI;AACA;;;AAGG;gBACH,UAAU,CACN,iBAAiB,EACjBA,SAAW,CAAC,IAAI,GAAGA,SAAW,CAAC,IAAI,CACtC,CAAC;gBAEF,gBAAgB,GAAG,mCAAmC,CAAC;gBACvD,YAAY,GAAG,8BAA8B,CAAC;AACjD,aAAA;AAAC,YAAA,OAAO,GAAG,EAAE;AACV;;;AAGG;AACN,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;KAC3C;AAEM,IAAA,OAAO,SAAS,CACnB,MAAc,EACd,WAAwB,EACxB,aAA6B,EAC7B,cAA8B,EAC9B,sBAA+B,EAC/B,iBAAoC,EAAA;QAEpC,MAAM,CAAC,gBAAgB,EAAE,YAAY,CAAC,GAClC,QAAQ,CAAC,uBAAuB,EAAE,CAAC;;AAGvC,QAAA,IAAI,CAAC,gBAAgB,IAAI,CAAC,YAAY,EAAE;YACpC,MAAM,CAAC,IAAI,CACP,CAAA,mBAAA,EAAsB,0BAA0B,CAAC,SAAS,0FAA0F,uCAAuC,CAAC,iBAAiB,CAAU,OAAA,EAAA,uCAAuC,CAAC,aAAa,CAAA,mBAAA,EAAsB,0BAA0B,CAAC,SAAS,CAA+D,6DAAA,CAAA,CACxY,CAAC;AAEF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;;QAGD,IAAI,YAAY,KAAK,8BAA8B,EAAE;AACjD,YAAA,MAAM,CAAC,IAAI,CACP,sBAAsB,0BAA0B,CAAC,SAAS,CAA8E,2EAAA,EAAA,0BAA0B,CAAC,SAAS,CAAA,WAAA,EAAc,mCAAmC,CAAc,WAAA,EAAA,0BAA0B,CAAC,SAAS,CAAA,kBAAA,CAAoB,CACtS,CAAC;AACL,SAAA;AAAM,aAAA;;AAGH,YAAA,MAAM,yBAAyB,GAC3B,QAAQ,CAAC,gCAAgC,CACrC,uCAAuC,CAAC,iBAAiB,EACzD,gBAAgB,EAChB,0BAA0B,CAAC,SAAS,EACpC,MAAM,CACT,CAAC;;AAEN,YAAA,yBAAyB,CAAC,QAAQ,CAAC,GAAG,CAAC;kBACjC,yBAAyB,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;kBACtC,yBAAyB,CAAC;AAEhC,YAAA,QAAQ,CAAC,gCAAgC,CACrC,uCAAuC,CAAC,aAAa,EACrD,YAAY,EACZ,0BAA0B,CAAC,SAAS,EACpC,MAAM,CACT,CAAC;AAEF,YAAA,MAAM,CAAC,IAAI,CACP,CAAA,+DAAA,EAAkE,0BAA0B,CAAC,SAAS,CAAoC,iCAAA,EAAA,yBAAyB,cAAc,0BAA0B,CAAC,SAAS,CAAA,kBAAA,CAAoB,CAC5O,CAAC;AACL,SAAA;AAED,QAAA,IACI,iBAAiB,CAAC,MAAM,KAAK,qBAAqB,CAAC,eAAe,EACpE;AACE,YAAA,MAAM,0BAA0B,CAC5BC,sBAAgD,CACnD,CAAC;AACL,SAAA;AAED,QAAA,OAAO,IAAI,QAAQ,CACf,MAAM,EACN,WAAW,EACX,aAAa,EACb,cAAc,EACd,sBAAsB,EACtB,gBAAgB,CACnB,CAAC;KACL;AAEM,IAAA,aAAa,CAAC,QAAgB,EAAA;QACjC,MAAM,OAAO,GACT,IAAI,gCAAgC,CAChC,UAAU,CAAC,GAAG,EACd,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,CAC1D,CAAC;QAEN,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,GAAG,MAAM,CAAC;AAEtE,QAAA,OAAO,CAAC,eAAe,CAAC,8BAA8B,CAAC,WAAW,CAAC;AAC/D,YAAA,eAAe,CAAC;AACpB,QAAA,OAAO,CAAC,eAAe,CAAC,8BAA8B,CAAC,QAAQ,CAAC;AAC5D,YAAA,QAAQ,CAAC;;AAIb,QAAA,OAAO,OAAO,CAAC;KAClB;IAEM,MAAM,2BAA2B,CACpC,gBAA+D,EAC/D,aAA6B,EAC7B,cAAgD,EAChD,qBAA4C,EAAA;AAE5C,QAAA,IAAI,aAEW,CAAC;AAEhB,QAAA,IAAI,gBAAgB,CAAC,MAAM,KAAK,UAAU,CAAC,YAAY,EAAE;YACrD,MAAM,aAAa,GACf,gBAAgB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YACjD,IAAI,CAAC,aAAa,EAAE;AAChB,gBAAA,MAAM,0BAA0B,CAC5BC,4BAAsD,CACzD,CAAC;AACL,aAAA;AACD,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;AACzC,gBAAA,MAAM,0BAA0B,CAC5BC,sCAAgE,CACnE,CAAC;AACL,aAAA;YAED,MAAM,cAAc,GAAG,aAAa,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;;YAG9D,IACI,CAAC,6BAA6B,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,EACjE;AACE,gBAAA,MAAM,0BAA0B,CAC5BC,oBAA8C,CACjD,CAAC;AACL,aAAA;;YAGD,MAAM,sBAAsB,GACxB,6BAA6B,CACzB,OAAO,CAAC,QAA6B,CACxC,CAAC;;YAGN,MAAM,QAAQ,GAAW,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;AACvD,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC5B,gBAAA,MAAM,0BAA0B,CAC5BC,oBAA8C,CACjD,CAAC;AACL,aAAA;AAED;;;;AAIG;AACH,YAAA,IAAI,sBAAsB,GAAG,QAAQ,KAAK,cAAc,EAAE;AACtD,gBAAA,MAAM,0BAA0B,CAC5BC,eAAyC,CAC5C,CAAC;AACL,aAAA;AAED,YAAA,IAAI,cAAc,CAAC;;YAEnB,IAAI;gBACA,cAAc,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC;AACxD,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,MAAM,0BAA0B,CAC5BC,sBAAgD,CACnD,CAAC;AACL,aAAA;;YAED,IAAI,cAAc,GAAG,oCAAoC,EAAE;AACvD,gBAAA,MAAM,0BAA0B,CAC5BC,aAAuC,CAC1C,CAAC;AACL,aAAA;;AAGD,YAAA,IAAI,MAAM,CAAC;YACX,IAAI;gBACA,MAAM,GAAG,YAAY,CAAC,cAAc,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;AAC7D,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,MAAM,0BAA0B,CAC5BD,sBAAgD,CACnD,CAAC;AACL,aAAA;AACD,YAAA,MAAM,eAAe,GAAG,CAAS,MAAA,EAAA,MAAM,EAAE,CAAC;AAE1C,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,CAAA,8DAAA,CAAgE,CACnE,CAAC;YACF,cAAc,CAAC,OAAO,CAClB,sBAAsB,CAAC,yBAAyB,CACnD,GAAG,eAAe,CAAC;YAEpB,IAAI;gBACA,aAAa;oBACT,MAAM,aAAa,CAAC,mBAAmB,CACnC,cAAc,CAAC,UAAU,EAAE,EAC3B,qBAAqB,CACxB,CAAC;AACT,aAAA;AAAC,YAAA,OAAO,KAAK,EAAE;gBACZ,IAAI,KAAK,YAAY,SAAS,EAAE;AAC5B,oBAAA,MAAM,KAAK,CAAC;AACf,iBAAA;AAAM,qBAAA;AACH,oBAAA,MAAM,qBAAqB,CACvB,oBAAoB,CAAC,YAAY,CACpC,CAAC;AACL,iBAAA;AACJ,aAAA;AACJ,SAAA;QAED,OAAO,IAAI,CAAC,sBAAsB,CAAC,aAAa,IAAI,gBAAgB,CAAC,CAAC;KACzE;AACJ;;;;"}