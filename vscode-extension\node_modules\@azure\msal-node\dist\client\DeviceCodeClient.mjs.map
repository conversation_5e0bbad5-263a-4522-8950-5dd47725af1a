{"version": 3, "file": "DeviceCodeClient.mjs", "sources": ["../../src/client/DeviceCodeClient.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAyBH;;;AAGG;AACG,MAAO,gBAAiB,SAAQ,UAAU,CAAA;AAC5C,IAAA,WAAA,CAAY,aAAkC,EAAA;QAC1C,KAAK,CAAC,aAAa,CAAC,CAAC;KACxB;AAED;;;;AAIG;IACI,MAAM,YAAY,CACrB,OAAgC,EAAA;QAEhC,MAAM,kBAAkB,GAAuB,MAAM,IAAI,CAAC,aAAa,CACnE,OAAO,CACV,CAAC;AACF,QAAA,OAAO,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AAC/C,QAAA,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;QAC5C,MAAM,QAAQ,GACV,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;AAEvE,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAChC,CAAC;;AAGF,QAAA,eAAe,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;AAChD,QAAA,OAAO,eAAe,CAAC,yBAAyB,CAC5C,QAAQ,EACR,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,OAAO,CACV,CAAC;KACL;AAED;;;AAGG;IACK,MAAM,aAAa,CACvB,OAAgC,EAAA;QAEhC,MAAM,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACvE,QAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CACxC,IAAI,CAAC,SAAS,CAAC,kBAAkB,EACjC,qBAAqB,CACxB,CAAC;QACF,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AACpD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACjD,QAAA,MAAM,UAAU,GAAsB;AAClC,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;YAC1C,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;YAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;YACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;SACzB,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC,sCAAsC,CAC9C,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,EACV,OAAO,CAAC,aAAa,CACxB,CAAC;KACL;AAED;;;AAGG;AACI,IAAA,0BAA0B,CAC7B,OAAgC,EAAA;AAEhC,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE7C,IAAI,OAAO,CAAC,oBAAoB,EAAE;YAC9B,uBAAuB,CAAC,uBAAuB,CAC3C,UAAU,EACV,OAAO,CAAC,oBAAoB,CAC/B,CAAC;AACL,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;KAChD;AAED;;;;;;;AAOG;IACK,MAAM,sCAAsC,CAChD,kBAA0B,EAC1B,WAAmB,EACnB,OAA+B,EAC/B,UAA6B,EAC7B,aAAqB,EAAA;AAErB,QAAA,MAAM,EACF,IAAI,EAAE,EACF,SAAS,EAAE,QAAQ,EACnB,WAAW,EAAE,UAAU,EACvB,gBAAgB,EAAE,eAAe,EACjC,UAAU,EAAE,SAAS,EACrB,QAAQ,EACR,OAAO,GACV,GACJ,GAAG,MAAM,IAAI,CAAC,eAAe,CAC1B,UAAU,EACV,kBAAkB,EAClB;AACI,YAAA,IAAI,EAAE,WAAW;AACjB,YAAA,OAAO,EAAE,OAAO;SACnB,EACD,aAAa,CAChB,CAAC;QAEF,OAAO;YACH,QAAQ;YACR,UAAU;YACV,eAAe;YACf,SAAS;YACT,QAAQ;YACR,OAAO;SACV,CAAC;KACL;AAED;;;AAGG;AACK,IAAA,iBAAiB,CAAC,OAAgC,EAAA;AACtD,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE7C,uBAAuB,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9D,QAAA,uBAAuB,CAAC,WAAW,CAC/B,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CACnC,CAAC;QAEF,IAAI,OAAO,CAAC,oBAAoB,EAAE;YAC9B,uBAAuB,CAAC,uBAAuB,CAC3C,UAAU,EACV,OAAO,CAAC,oBAAoB,CAC/B,CAAC;AACL,SAAA;QAED,IACI,OAAO,CAAC,MAAM;AACd,aAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB;gBACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5D;AACE,YAAA,uBAAuB,CAAC,SAAS,CAC7B,UAAU,EACV,OAAO,CAAC,MAAM,EACd,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAC7C,CAAC;AACL,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;KAChD;AAED;;;;;AAKG;AACK,IAAA,eAAe,CACnB,wBAAgC,EAChC,oBAA6B,EAC7B,uBAAiC,EAAA;AAEjC,QAAA,IAAI,uBAAuB,EAAE;AACzB,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,oEAAoE,CACvE,CAAC;AACF,YAAA,MAAM,qBAAqB,CACvB,oBAAoB,CAAC,0BAA0B,CAClD,CAAC;AACL,SAAA;AAAM,aAAA,IACH,oBAAoB;AACpB,YAAA,oBAAoB,GAAG,wBAAwB;AAC/C,YAAA,SAAS,CAAC,UAAU,EAAE,GAAG,oBAAoB,EAC/C;YACE,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAAiF,8EAAA,EAAA,oBAAoB,CAAE,CAAA,CAC1G,CAAC;AACF,YAAA,MAAM,qBAAqB,CACvB,oBAAoB,CAAC,kBAAkB,CAC1C,CAAC;AACL,SAAA;AAAM,aAAA,IAAI,SAAS,CAAC,UAAU,EAAE,GAAG,wBAAwB,EAAE;AAC1D,YAAA,IAAI,oBAAoB,EAAE;gBACtB,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CAAoI,iIAAA,EAAA,oBAAoB,CAAE,CAAA,CAC7J,CAAC;AACL,aAAA;YACD,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAA2D,wDAAA,EAAA,wBAAwB,CAAE,CAAA,CACxF,CAAC;AACF,YAAA,MAAM,qBAAqB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;AACvE,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;AAIG;AACK,IAAA,MAAM,0BAA0B,CACpC,OAAgC,EAChC,kBAAsC,EAAA;QAEtC,MAAM,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACvE,QAAA,MAAM,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CACxC,IAAI,CAAC,SAAS,CAAC,aAAa,EAC5B,qBAAqB,CACxB,CAAC;QACF,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAC3C,OAAO,EACP,kBAAkB,CACrB,CAAC;AACF,QAAA,MAAM,OAAO,GACT,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAErC,QAAA,MAAM,oBAAoB,GAAG,OAAO,CAAC,OAAO;cACtC,SAAS,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,OAAO;cACxC,SAAS,CAAC;QAChB,MAAM,wBAAwB,GAC1B,SAAS,CAAC,UAAU,EAAE,GAAG,kBAAkB,CAAC,SAAS,CAAC;AAC1D,QAAA,MAAM,oBAAoB,GAAG,kBAAkB,CAAC,QAAQ,GAAG,IAAI,CAAC;AAEhE;;;AAGG;AACH,QAAA,OACI,IAAI,CAAC,eAAe,CAChB,wBAAwB,EACxB,oBAAoB,EACpB,OAAO,CAAC,MAAM,CACjB,EACH;AACE,YAAA,MAAM,UAAU,GAAsB;AAClC,gBAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;gBAC1C,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;gBAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;gBACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;gBAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;aACzB,CAAC;AACF,YAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAClD,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,EACV,OAAO,CAAC,aAAa,CACxB,CAAC;YAEF,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;;gBAEtC,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,SAAS,CAAC,qBAAqB,EAAE;AACzD,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,0CAA0C,CAC7C,CAAC;AACF,oBAAA,MAAM,SAAS,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;AAC/C,iBAAA;AAAM,qBAAA;;AAEH,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,6CAA6C,CAChD,CAAC;AACF,oBAAA,MAAM,eAAe,CACjB,cAAc,CAAC,iBAAiB,EAChC,QAAQ,CAAC,IAAI,CAAC,KAAK,CACtB,CAAC;AACL,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,wDAAwD,CAC3D,CAAC;gBACF,OAAO,QAAQ,CAAC,IAAI,CAAC;AACxB,aAAA;AACJ,SAAA;AAED;;;AAGG;AACH,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;AAC1D,QAAA,MAAM,qBAAqB,CACvB,oBAAoB,CAAC,sBAAsB,CAC9C,CAAC;KACL;AAED;;;;AAIG;IACK,sBAAsB,CAC1B,OAAgC,EAChC,kBAAsC,EAAA;AAEtC,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE7C,uBAAuB,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9D,QAAA,uBAAuB,CAAC,WAAW,CAC/B,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CACnC,CAAC;QACF,uBAAuB,CAAC,YAAY,CAChC,UAAU,EACV,SAAS,CAAC,iBAAiB,CAC9B,CAAC;QACF,uBAAuB,CAAC,aAAa,CACjC,UAAU,EACV,kBAAkB,CAAC,UAAU,CAChC,CAAC;AACF,QAAA,MAAM,aAAa,GACf,OAAO,CAAC,aAAa;AACrB,YAAA,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;AAChD,QAAA,uBAAuB,CAAC,gBAAgB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;AACpE,QAAA,uBAAuB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAClD,uBAAuB,CAAC,cAAc,CAClC,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,WAAW,CAC1B,CAAC;AACF,QAAA,uBAAuB,CAAC,uBAAuB,CAC3C,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CACpC,CAAC;AACF,QAAA,uBAAuB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,uBAAuB,CAAC,kBAAkB,CACtC,UAAU,EACV,IAAI,CAAC,sBAAsB,CAC9B,CAAC;AACL,SAAA;QAED,IACI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,aAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB;gBACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5D;AACE,YAAA,uBAAuB,CAAC,SAAS,CAC7B,UAAU,EACV,OAAO,CAAC,MAAM,EACd,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAC7C,CAAC;AACL,SAAA;AACD,QAAA,OAAO,QAAQ,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;KAChD;AACJ;;;;"}