{"version": 3, "file": "ManagedIdentityError.mjs", "sources": ["../../src/error/ManagedIdentityError.ts"], "sourcesContent": [null], "names": ["ManagedIdentityErrorCodes.invalidFileExtension", "ManagedIdentityErrorCodes.invalidFilePath", "ManagedIdentityErrorCodes.invalidManagedIdentityIdType", "ManagedIdentityErrorCodes.invalidSecret", "ManagedIdentityErrorCodes.platformNotSupported", "ManagedIdentityErrorCodes.missingId", "ManagedIdentityErrorCodes.MsiEnvironmentVariableUrlMalformedErrorCodes", "ManagedIdentityErrorCodes.networkUnavailable", "ManagedIdentityErrorCodes.unableToCreateAzureArc", "ManagedIdentityErrorCodes.unableToCreateCloudShell", "ManagedIdentityErrorCodes.unableToCreateSource", "ManagedIdentityErrorCodes.unableToReadSecretFile", "ManagedIdentityErrorCodes.userAssignedNotAvailableAtRuntime", "ManagedIdentityErrorCodes.wwwAuthenticateHeaderMissing", "ManagedIdentityErrorCodes.wwwAuthenticateHeaderUnsupportedFormat"], "mappings": ";;;;;;AAAA;;;AAGG;AAOH;;AAEG;AACU,MAAA,4BAA4B,GAAG;AACxC,IAAA,CAACA,oBAA8C,GAC3C,4EAA4E;AAChF,IAAA,CAACC,eAAyC,GACtC,yFAAyF;AAC7F,IAAA,CAACC,4BAAsD,GACnD,mDAAmD;AACvD,IAAA,CAACC,aAAuC,GACpC,oGAAoG;AACxG,IAAA,CAACC,oBAA8C,GAC3C,wFAAwF;AAC5F,IAAA,CAACC,SAAmC,GAChC,0CAA0C;IAC9C,CAACC,4CAAsE;AAClE,SAAA,iCAAiC,GAAG,CAAA,wBAAA,EAA2B,uCAAuC,CAAC,iCAAiC,CAAsC,oCAAA,CAAA;IACnL,CAACA,4CAAsE;AAClE,SAAA,iBAAiB,GAAG,CAAA,wBAAA,EAA2B,uCAAuC,CAAC,iBAAiB,CAAsC,oCAAA,CAAA;IACnJ,CAACA,4CAAsE;AAClE,SAAA,aAAa,GAAG,CAAA,wBAAA,EAA2B,uCAAuC,CAAC,aAAa,CAAsC,oCAAA,CAAA;IAC3I,CAACA,4CAAsE;AAClE,SAAA,YAAY,GAAG,CAAA,wBAAA,EAA2B,uCAAuC,CAAC,YAAY,CAAsC,oCAAA,CAAA;AACzI,IAAA,CAACC,kBAA4C,GACzC,qFAAqF;AACzF,IAAA,CAACC,sBAAgD,GAC7C,2DAA2D;AAC/D,IAAA,CAACC,wBAAkD,GAC/C,6DAA6D;AACjE,IAAA,CAACC,oBAA8C,GAC3C,4EAA4E;AAChF,IAAA,CAACC,sBAAgD,GAC7C,iCAAiC;AACrC,IAAA,CAACC,iCAA2D,GACxD,sGAAsG;AAC1G,IAAA,CAACC,4BAAsD,GACnD,8GAA8G;AAClH,IAAA,CAACC,sCAAgE,GAC7D,+HAA+H;EACrI;AAEI,MAAO,oBAAqB,SAAQ,SAAS,CAAA;AAC/C,IAAA,WAAA,CAAY,SAAiB,EAAA;QACzB,KAAK,CAAC,SAAS,EAAE,4BAA4B,CAAC,SAAS,CAAC,CAAC,CAAC;AAC1D,QAAA,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;QACnC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,oBAAoB,CAAC,SAAS,CAAC,CAAC;KAC/D;AACJ,CAAA;AAEK,SAAU,0BAA0B,CACtC,SAAiB,EAAA;AAEjB,IAAA,OAAO,IAAI,oBAAoB,CAAC,SAAS,CAAC,CAAC;AAC/C;;;;"}