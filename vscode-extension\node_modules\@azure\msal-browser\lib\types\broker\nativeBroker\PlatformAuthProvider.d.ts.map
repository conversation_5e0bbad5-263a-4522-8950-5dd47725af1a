{"version": 3, "file": "PlatformAuthProvider.d.ts", "sourceRoot": "", "sources": ["../../../../src/broker/nativeBroker/PlatformAuthProvider.ts"], "names": [], "mappings": "AAKA,OAAO,EACH,aAAa,EACb,kBAAkB,EAClB,MAAM,EACN,oBAAoB,EAEvB,MAAM,4BAA4B,CAAC;AAEpC,OAAO,EACH,oBAAoB,EAEvB,MAAM,+BAA+B,CAAC;AAEvC,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AAQjE;;;;;GAKG;AACH,wBAAsB,yBAAyB,CAC3C,aAAa,CAAC,EAAE,aAAa,EAC7B,UAAU,CAAC,EAAE,kBAAkB,EAC/B,aAAa,CAAC,EAAE,MAAM,GACvB,OAAO,CAAC,OAAO,CAAC,CAiBlB;AAED,wBAAsB,uBAAuB,CACzC,MAAM,EAAE,MAAM,EACd,iBAAiB,EAAE,kBAAkB,EACrC,aAAa,EAAE,MAAM,EACrB,4BAA4B,CAAC,EAAE,MAAM,GACtC,OAAO,CAAC,oBAAoB,GAAG,SAAS,CAAC,CAuC3C;AAED;;;;GAIG;AACH,wBAAgB,2BAA2B,IAAI,OAAO,CASrD;AAED;;;;;;GAMG;AACH,wBAAgB,qBAAqB,CACjC,MAAM,EAAE,oBAAoB,EAC5B,MAAM,EAAE,MAAM,EACd,oBAAoB,CAAC,EAAE,oBAAoB,EAC3C,oBAAoB,CAAC,EAAE,oBAAoB,GAC5C,OAAO,CAkCT"}