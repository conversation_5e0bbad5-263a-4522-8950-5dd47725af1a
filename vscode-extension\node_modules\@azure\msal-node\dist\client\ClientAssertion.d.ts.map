{"version": 3, "file": "ClientAssertion.d.ts", "sourceRoot": "", "sources": ["../../src/client/ClientAssertion.ts"], "names": [], "mappings": "AAaA,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAI7D;;;GAGG;AACH,qBAAa,eAAe;IACxB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,UAAU,CAAS;IAC3B,OAAO,CAAC,UAAU,CAAS;IAC3B,OAAO,CAAC,SAAS,CAAU;IAC3B,OAAO,CAAC,cAAc,CAAS;IAC/B,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,WAAW,CAAS;IAC5B,OAAO,CAAC,iBAAiB,CAAgB;IAEzC;;;OAGG;WACW,aAAa,CAAC,SAAS,EAAE,MAAM,GAAG,eAAe;IAM/D;;;;;;OAMG;WACW,eAAe,CACzB,UAAU,EAAE,MAAM,EAClB,UAAU,EAAE,MAAM,EAClB,iBAAiB,CAAC,EAAE,MAAM,GAC3B,eAAe;IAYlB;;;;;OAKG;WACW,mCAAmC,CAC7C,UAAU,EAAE,MAAM,EAClB,UAAU,EAAE,MAAM,EAClB,iBAAiB,CAAC,EAAE,MAAM,GAC3B,eAAe;IAYlB;;;;;OAKG;IACI,MAAM,CACT,cAAc,EAAE,cAAc,EAC9B,MAAM,EAAE,MAAM,EACd,WAAW,EAAE,MAAM,GACpB,MAAM;IA0BT;;OAEG;IACH,OAAO,CAAC,SAAS;IA8CjB;;OAEG;IACH,OAAO,CAAC,SAAS;IAIjB;;;OAGG;WACW,gBAAgB,CAAC,iBAAiB,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;CAoB3E"}