{"version": 3, "file": "SignInResult.d.ts", "sourceRoot": "", "sources": ["../../../../../../src/custom_auth/sign_in/auth_flow/result/SignInResult.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,qBAAqB,EAAE,MAAM,yDAAyD,CAAC;AAChG,OAAO,EAAE,kBAAkB,EAAE,MAAM,+CAA+C,CAAC;AACnF,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAC3D,OAAO,EAAE,uBAAuB,EAAE,MAAM,qCAAqC,CAAC;AAC9E,OAAO,EAAE,2BAA2B,EAAE,MAAM,yCAAyC,CAAC;AACtF,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,EAAE,oBAAoB,EAAE,MAAM,kCAAkC,CAAC;AAKxE,qBAAa,YAAa,SAAQ,kBAAkB,CAChD,iBAAiB,EACjB,WAAW,EACX,qBAAqB,CACxB;IACG;;;OAGG;gBACS,KAAK,EAAE,iBAAiB,EAAE,UAAU,CAAC,EAAE,qBAAqB;IAIxE;;;;OAIG;IACH,MAAM,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,GAAG,YAAY;IAOpD;;OAEG;IACH,QAAQ,IAAI,IAAI,IAAI,YAAY,GAAG;QAAE,KAAK,EAAE,iBAAiB,CAAA;KAAE;IAI/D;;OAEG;IACH,cAAc,IAAI,IAAI,IAAI,YAAY,GAAG;QACrC,KAAK,EAAE,uBAAuB,CAAC;KAClC;IAID;;OAEG;IACH,kBAAkB,IAAI,IAAI,IAAI,YAAY,GAAG;QACzC,KAAK,EAAE,2BAA2B,CAAC;KACtC;IAID;;OAEG;IACH,WAAW,IAAI,IAAI,IAAI,YAAY,GAAG;QAAE,KAAK,EAAE,oBAAoB,CAAA;KAAE;CAGxE;AAED;;;;;;;GAOG;AACH,MAAM,MAAM,iBAAiB,GACvB,uBAAuB,GACvB,2BAA2B,GAC3B,iBAAiB,GACjB,oBAAoB,CAAC"}