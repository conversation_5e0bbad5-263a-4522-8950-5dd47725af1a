{"version": 3, "file": "NodeAuthError.mjs", "sources": ["../../src/error/NodeAuthError.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAIH;;AAEG;AACU,MAAA,oBAAoB,GAAG;AAChC,IAAA,0BAA0B,EAAE;AACxB,QAAA,IAAI,EAAE,sCAAsC;AAC5C,QAAA,IAAI,EAAE,iEAAiE;AAC1E,KAAA;AACD,IAAA,uBAAuB,EAAE;AACrB,QAAA,IAAI,EAAE,4BAA4B;AAClC,QAAA,IAAI,EAAE,yEAAyE;AAClF,KAAA;AACD,IAAA,oBAAoB,EAAE;AAClB,QAAA,IAAI,EAAE,0BAA0B;AAChC,QAAA,IAAI,EAAE,wGAAwG;AACjH,KAAA;AACD,IAAA,sBAAsB,EAAE;AACpB,QAAA,IAAI,EAAE,2BAA2B;AACjC,QAAA,IAAI,EAAE,gCAAgC;AACzC,KAAA;AACD,IAAA,2BAA2B,EAAE;AACzB,QAAA,IAAI,EAAE,gCAAgC;AACtC,QAAA,IAAI,EAAE,wDAAwD;AACjE,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,yBAAyB;AAC/B,QAAA,IAAI,EAAE,4DAA4D;AACrE,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,iBAAiB;AACvB,QAAA,IAAI,EAAE,uEAAuE;AAChF,KAAA;AACD,IAAA,iBAAiB,EAAE;AACf,QAAA,IAAI,EAAE,4CAA4C;AAClD,QAAA,IAAI,EAAE,oEAAoE;AAC7E,KAAA;EACH;AAEI,MAAO,aAAc,SAAQ,SAAS,CAAA;IACxC,WAAY,CAAA,SAAiB,EAAE,YAAqB,EAAA;AAChD,QAAA,KAAK,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;KAC/B;AAED;;AAEG;AACH,IAAA,OAAO,qCAAqC,GAAA;AACxC,QAAA,OAAO,IAAI,aAAa,CACpB,oBAAoB,CAAC,0BAA0B,CAAC,IAAI,EACpD,CAAA,EAAG,oBAAoB,CAAC,0BAA0B,CAAC,IAAI,CAAA,CAAE,CAC5D,CAAC;KACL;AAED;;AAEG;AACH,IAAA,OAAO,kCAAkC,GAAA;AACrC,QAAA,OAAO,IAAI,aAAa,CACpB,oBAAoB,CAAC,uBAAuB,CAAC,IAAI,EACjD,CAAA,EAAG,oBAAoB,CAAC,uBAAuB,CAAC,IAAI,CAAA,CAAE,CACzD,CAAC;KACL;AAED;;AAEG;AACH,IAAA,OAAO,+BAA+B,GAAA;AAClC,QAAA,OAAO,IAAI,aAAa,CACpB,oBAAoB,CAAC,oBAAoB,CAAC,IAAI,EAC9C,CAAA,EAAG,oBAAoB,CAAC,oBAAoB,CAAC,IAAI,CAAA,CAAE,CACtD,CAAC;KACL;AAED;;AAEG;AACH,IAAA,OAAO,iCAAiC,GAAA;AACpC,QAAA,OAAO,IAAI,aAAa,CACpB,oBAAoB,CAAC,sBAAsB,CAAC,IAAI,EAChD,CAAA,EAAG,oBAAoB,CAAC,sBAAsB,CAAC,IAAI,CAAA,CAAE,CACxD,CAAC;KACL;AAED;;AAEG;AACH,IAAA,OAAO,sCAAsC,GAAA;AACzC,QAAA,OAAO,IAAI,aAAa,CACpB,oBAAoB,CAAC,2BAA2B,CAAC,IAAI,EACrD,CAAA,EAAG,oBAAoB,CAAC,2BAA2B,CAAC,IAAI,CAAA,CAAE,CAC7D,CAAC;KACL;AAED;;AAEG;AACH,IAAA,OAAO,gCAAgC,GAAA;AACnC,QAAA,OAAO,IAAI,aAAa,CACpB,oBAAoB,CAAC,qBAAqB,CAAC,IAAI,EAC/C,CAAA,EAAG,oBAAoB,CAAC,qBAAqB,CAAC,IAAI,CAAA,CAAE,CACvD,CAAC;KACL;AAED;;AAEG;AACH,IAAA,OAAO,wBAAwB,GAAA;AAC3B,QAAA,OAAO,IAAI,aAAa,CACpB,oBAAoB,CAAC,kBAAkB,CAAC,IAAI,EAC5C,oBAAoB,CAAC,kBAAkB,CAAC,IAAI,CAC/C,CAAC;KACL;AAED;;AAEG;AACH,IAAA,OAAO,4BAA4B,GAAA;AAC/B,QAAA,OAAO,IAAI,aAAa,CACpB,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,EAC3C,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAC9C,CAAC;KACL;AACJ;;;;"}