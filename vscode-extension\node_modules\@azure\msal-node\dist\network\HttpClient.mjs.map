{"version": 3, "file": "HttpClient.mjs", "sources": ["../../src/network/HttpClient.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;;;AAAA;;;AAGG;AAaH;;AAEG;MACU,UAAU,CAAA;IAInB,WACI,CAAA,QAAiB,EACjB,kBAA2D,EAAA;AAE3D,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,IAAI,EAAE,CAAC;KACtD;AAED;;;;AAIG;AACH,IAAA,MAAM,mBAAmB,CACrB,GAAW,EACX,OAA+B,EAC/B,OAAgB,EAAA;QAEhB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO,sBAAsB,CACzB,GAAG,EACH,IAAI,CAAC,QAAQ,EACb,UAAU,CAAC,GAAG,EACd,OAAO,EACP,IAAI,CAAC,kBAAuC,EAC5C,OAAO,CACV,CAAC;AACL,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,sBAAsB,CACzB,GAAG,EACH,UAAU,CAAC,GAAG,EACd,OAAO,EACP,IAAI,CAAC,kBAAwC,EAC7C,OAAO,CACV,CAAC;AACL,SAAA;KACJ;AAED;;;;AAIG;AACH,IAAA,MAAM,oBAAoB,CACtB,GAAW,EACX,OAA+B,EAAA;QAE/B,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,OAAO,sBAAsB,CACzB,GAAG,EACH,IAAI,CAAC,QAAQ,EACb,UAAU,CAAC,IAAI,EACf,OAAO,EACP,IAAI,CAAC,kBAAuC,CAC/C,CAAC;AACL,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,sBAAsB,CACzB,GAAG,EACH,UAAU,CAAC,IAAI,EACf,OAAO,EACP,IAAI,CAAC,kBAAwC,CAChD,CAAC;AACL,SAAA;KACJ;AACJ,CAAA;AAED,MAAM,sBAAsB,GAAG,CAC3B,oBAA4B,EAC5B,cAAsB,EACtB,UAAkB,EAClB,OAA+B,EAC/B,YAAgC,EAChC,OAAgB,KACa;AAC7B,IAAA,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,oBAAoB,CAAC,CAAC;AACrD,IAAA,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,CAAC;;AAGzC,IAAA,MAAM,OAAO,GAAG,OAAO,EAAE,OAAO,IAAK,EAA6B,CAAC;AACnE,IAAA,MAAM,oBAAoB,GAAyB;QAC/C,IAAI,EAAE,QAAQ,CAAC,QAAQ;QACvB,IAAI,EAAE,QAAQ,CAAC,IAAI;AACnB,QAAA,MAAM,EAAE,SAAS;QACjB,IAAI,EAAE,cAAc,CAAC,QAAQ;AAC7B,QAAA,OAAO,EAAE,OAAO;KACnB,CAAC;IAEF,IAAI,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE;QAClD,oBAAoB,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AAC7D,KAAA;;IAGD,IAAI,wBAAwB,GAAW,EAAE,CAAC;AAC1C,IAAA,IAAI,UAAU,KAAK,UAAU,CAAC,IAAI,EAAE;AAChC,QAAA,MAAM,IAAI,GAAG,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC;QACjC,wBAAwB;YACpB,qDAAqD;gBACrD,CAAmB,gBAAA,EAAA,IAAI,CAAC,MAAM,CAAM,IAAA,CAAA;gBACpC,CAAO,IAAA,EAAA,IAAI,EAAE,CAAC;AACrB,KAAA;AAAM,SAAA;;AAEH,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,oBAAoB,CAAC,OAAO,GAAG,OAAO,CAAC;AAC1C,SAAA;AACJ,KAAA;IACD,MAAM,qBAAqB,GACvB,CAAA,EAAG,UAAU,CAAC,WAAW,EAAE,CAAI,CAAA,EAAA,cAAc,CAAC,IAAI,CAAe,aAAA,CAAA;QACjE,CAAS,MAAA,EAAA,cAAc,CAAC,IAAI,CAAM,IAAA,CAAA;QAClC,uBAAuB;QACvB,wBAAwB;AACxB,QAAA,MAAM,CAAC;IAEX,OAAO,IAAI,OAAO,CAAqB,CAAC,OAAO,EAAE,MAAM,KAAI;QACvD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAEnD,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,MAAK;gBACvB,OAAO,CAAC,OAAO,EAAE,CAAC;AAClB,gBAAA,MAAM,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;AAC1C,aAAC,CAAC,CAAC;AACN,SAAA;QAED,OAAO,CAAC,GAAG,EAAE,CAAC;;QAGd,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAI;YACvC,MAAM,eAAe,GACjB,QAAQ,EAAE,UAAU,IAAI,WAAW,CAAC,YAAY,CAAC;AACrD,YAAA,IACI,eAAe,GAAG,WAAW,CAAC,mBAAmB;AACjD,gBAAA,eAAe,GAAG,WAAW,CAAC,iBAAiB,EACjD;gBACE,OAAO,CAAC,OAAO,EAAE,CAAC;gBAClB,MAAM,CAAC,OAAO,EAAE,CAAC;AACjB,gBAAA,MAAM,CACF,IAAI,KAAK,CACL,CAAA,6CAAA,EACI,QAAQ,CAAC,UACb,CACI,uBAAA,EAAA,QAAQ,EAAE,aAAa,IAAI,SAC/B,CAAE,CAAA,CACL,CACJ,CAAC;AACL,aAAA;;AAGD,YAAA,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;YAEpC,MAAM,IAAI,GAAa,EAAE,CAAC;YAC1B,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,KAAI;AACxB,gBAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrB,aAAC,CAAC,CAAC;AAEH,YAAA,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,MAAK;;AAElB,gBAAA,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;;gBAGvD,MAAM,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;;AAEjD,gBAAA,MAAM,cAAc,GAAG,QAAQ,CAC3B,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACnC,CAAC;;AAEF,gBAAA,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC;qBACnC,KAAK,CAAC,GAAG,CAAC;qBACV,KAAK,CAAC,CAAC,CAAC;qBACR,IAAI,CAAC,GAAG,CAAC,CAAC;;gBAEf,MAAM,IAAI,GAAG,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;;AAGzD,gBAAA,MAAM,YAAY,GAAG,eAAe,CAAC,KAAK,CACtC,CAAC,EACD,eAAe,CAAC,MAAM,GAAG,CAAC,CAC7B,CAAC;;AAGF,gBAAA,MAAM,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AAC1B,gBAAA,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAC5B;;;;;AAKG;AACH,oBAAA,MAAM,cAAc,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;AAC5D,oBAAA,MAAM,SAAS,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;AACpC,oBAAA,IAAI,WAAW,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;;oBAGpC,IAAI;wBACA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;;AAGvC,wBAAA,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;4BACtC,WAAW,GAAG,MAAM,CAAC;AACxB,yBAAA;AACJ,qBAAA;AAAC,oBAAA,OAAO,CAAC,EAAE;;AAEX,qBAAA;AAED,oBAAA,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AACxC,iBAAC,CAAC,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAE5C,MAAM,aAAa,GAAG,OAAiC,CAAC;gBACxD,MAAM,eAAe,GAAG,YAAY,CAAC,kBAAkB,CACnD,aAAa,EACb,SAAS,CACL,cAAc,EACd,aAAa,EACb,aAAa,EACb,IAAI,CACF,EACN,cAAc,CACjB,CAAC;AAEF,gBAAA,IACI,CAAC,cAAc,GAAG,UAAU,CAAC,mBAAmB;AAC5C,oBAAA,cAAc,GAAG,UAAU,CAAC,iBAAiB;;AAEjD,oBAAA,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC;wBACzB,SAAS,CAAC,qBAAqB,EACrC;oBACE,OAAO,CAAC,OAAO,EAAE,CAAC;AACrB,iBAAA;gBACD,OAAO,CAAC,eAAe,CAAC,CAAC;AAC7B,aAAC,CAAC,CAAC;YAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,KAAI;gBACzB,OAAO,CAAC,OAAO,EAAE,CAAC;gBAClB,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,MAAM,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACxC,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;QAEH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,KAAI;YAC1B,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACxC,SAAC,CAAC,CAAC;AACP,KAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF,MAAM,sBAAsB,GAAG,CAC3B,SAAiB,EACjB,UAAkB,EAClB,OAA+B,EAC/B,YAAiC,EACjC,OAAgB,KACa;AAC7B,IAAA,MAAM,aAAa,GAAG,UAAU,KAAK,UAAU,CAAC,IAAI,CAAC;AACrD,IAAA,MAAM,IAAI,GAAW,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC;AAEzC,IAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC;AAC/B,IAAA,MAAM,OAAO,GAAG,OAAO,EAAE,OAAO,IAAK,EAA6B,CAAC;AACnE,IAAA,MAAM,aAAa,GAAyB;AACxC,QAAA,MAAM,EAAE,UAAU;AAClB,QAAA,OAAO,EAAE,OAAO;AAChB,QAAA,GAAG,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC;KACxC,CAAC;IAEF,IAAI,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE;QAClD,aAAa,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACvD,KAAA;AAED,IAAA,IAAI,aAAa,EAAE;;QAEf,aAAa,CAAC,OAAO,GAAG;YACpB,GAAG,aAAa,CAAC,OAAO;YACxB,gBAAgB,EAAE,IAAI,CAAC,MAAM;SAChC,CAAC;AACL,KAAA;AAAM,SAAA;;AAEH,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC;AACnC,SAAA;AACJ,KAAA;IAED,OAAO,IAAI,OAAO,CAAqB,CAAC,OAAO,EAAE,MAAM,KAAI;AACvD,QAAA,IAAI,OAA2B,CAAC;;AAEhC,QAAA,IAAI,aAAa,CAAC,QAAQ,KAAK,OAAO,EAAE;AACpC,YAAA,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACzC,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAC1C,SAAA;AAED,QAAA,IAAI,aAAa,EAAE;AACf,YAAA,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACvB,SAAA;AAED,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,MAAK;gBACvB,OAAO,CAAC,OAAO,EAAE,CAAC;AAClB,gBAAA,MAAM,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;AAC1C,aAAC,CAAC,CAAC;AACN,SAAA;QAED,OAAO,CAAC,GAAG,EAAE,CAAC;QAEd,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,QAAQ,KAAI;AAChC,YAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;AACjC,YAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAoB,CAAC;AACjD,YAAA,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;YAE7C,MAAM,IAAI,GAAa,EAAE,CAAC;YAC1B,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,KAAI;AAC1B,gBAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrB,aAAC,CAAC,CAAC;AAEH,YAAA,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,MAAK;;AAEpB,gBAAA,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAEjD,MAAM,aAAa,GAAG,OAAiC,CAAC;gBACxD,MAAM,eAAe,GAAG,YAAY,CAAC,kBAAkB,CACnD,aAAa,EACb,SAAS,CACL,UAAU,EACV,aAAa,EACb,aAAa,EACb,IAAI,CACF,EACN,UAAU,CACb,CAAC;AAEF,gBAAA,IACI,CAAC,UAAU,GAAG,UAAU,CAAC,mBAAmB;AACxC,oBAAA,UAAU,GAAG,UAAU,CAAC,iBAAiB;;AAE7C,oBAAA,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC;wBACzB,SAAS,CAAC,qBAAqB,EACrC;oBACE,OAAO,CAAC,OAAO,EAAE,CAAC;AACrB,iBAAA;gBACD,OAAO,CAAC,eAAe,CAAC,CAAC;AAC7B,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;QAEH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,KAAI;YAC1B,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACxC,SAAC,CAAC,CAAC;AACP,KAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF;;;;;;;AAOG;AACH,MAAM,SAAS,GAAG,CACd,UAAkB,EAClB,aAAiC,EACjC,OAA+B,EAC/B,IAAY,KACZ;AACA;;;;;;AAMG;AAEH,IAAA,IAAI,UAAU,CAAC;IACf,IAAI;AACA,QAAA,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACjC,KAAA;AAAC,IAAA,OAAO,KAAK,EAAE;AACZ,QAAA,IAAI,SAAS,CAAC;AACd,QAAA,IAAI,sBAAsB,CAAC;AAC3B,QAAA,IACI,UAAU,IAAI,UAAU,CAAC,wBAAwB;AACjD,YAAA,UAAU,IAAI,UAAU,CAAC,sBAAsB,EACjD;YACE,SAAS,GAAG,cAAc,CAAC;YAC3B,sBAAsB,GAAG,UAAU,CAAC;AACvC,SAAA;AAAM,aAAA,IACH,UAAU,IAAI,UAAU,CAAC,wBAAwB;AACjD,YAAA,UAAU,IAAI,UAAU,CAAC,sBAAsB,EACjD;YACE,SAAS,GAAG,cAAc,CAAC;YAC3B,sBAAsB,GAAG,UAAU,CAAC;AACvC,SAAA;AAAM,aAAA;YACH,SAAS,GAAG,eAAe,CAAC;YAC5B,sBAAsB,GAAG,YAAY,CAAC;AACzC,SAAA;AAED,QAAA,UAAU,GAAG;AACT,YAAA,KAAK,EAAE,SAAS;AAChB,YAAA,iBAAiB,EAAE,CAAG,EAAA,sBAAsB,CAAsC,mCAAA,EAAA,UAAU,0BACxF,aAAa,IAAI,SACrB,CAAA,WAAA,EAAc,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAE,CAAA;SAC1C,CAAC;AACL,KAAA;AAED,IAAA,OAAO,UAAU,CAAC;AACtB,CAAC;;;;"}