import { AuthError } from "./AuthError.js";
/**
 * Error thrown when there is an error with the server code, for example, unavailability.
 */
export declare class ServerError extends AuthError {
    /**
     * Server error number;
     */
    readonly errorNo?: string;
    /**
     * Http status number;
     */
    readonly status?: number;
    constructor(errorCode?: string, errorMessage?: string, subError?: string, errorNo?: string, status?: number);
}
//# sourceMappingURL=ServerError.d.ts.map