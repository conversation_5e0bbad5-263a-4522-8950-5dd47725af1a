{"version": 3, "file": "Base64Decode.mjs", "sources": ["../../src/encode/Base64Decode.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.invalidBase64String"], "mappings": ";;;;;AAAA;;;AAGG;AAOH;;;AAGG;AAEH;;;AAGG;AACG,SAAU,YAAY,CAAC,KAAa,EAAA;IACtC,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3D,CAAC;AAED;;;AAGG;AACG,SAAU,cAAc,CAAC,YAAoB,EAAA;AAC/C,IAAA,IAAI,aAAa,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACvE,IAAA,QAAQ,aAAa,CAAC,MAAM,GAAG,CAAC;AAC5B,QAAA,KAAK,CAAC;YACF,MAAM;AACV,QAAA,KAAK,CAAC;YACF,aAAa,IAAI,IAAI,CAAC;YACtB,MAAM;AACV,QAAA,KAAK,CAAC;YACF,aAAa,IAAI,GAAG,CAAC;YACrB,MAAM;AACV,QAAA;AACI,YAAA,MAAM,sBAAsB,CACxBA,mBAAyC,CAC5C,CAAC;AACT,KAAA;AACD,IAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;IACtC,OAAO,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACpE;;;;"}