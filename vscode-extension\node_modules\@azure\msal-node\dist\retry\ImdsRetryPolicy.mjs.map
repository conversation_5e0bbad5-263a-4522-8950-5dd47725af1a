{"version": 3, "file": "ImdsRetryPolicy.mjs", "sources": ["../../src/retry/ImdsRetryPolicy.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAMH,MAAM,8CAA8C,GAAkB;AAClE,IAAA,UAAU,CAAC,SAAS;AACpB,IAAA,UAAU,CAAC,eAAe;AAC1B,IAAA,UAAU,CAAC,IAAI;AACf,IAAA,UAAU,CAAC,iBAAiB;CAC/B,CAAC;AAEF,MAAM,gCAAgC,GAAG,CAAC,CAAC;AAC3C,MAAM,2BAA2B,GAAG,CAAC,CAAC;AAEtC,MAAM,0BAA0B,GAAW,IAAI,CAAC;AAChD,MAAM,0BAA0B,GAAW,IAAI,CAAC;AAChD,MAAM,4BAA4B,GAAW,IAAI,CAAC;AAElD,MAAM,+BAA+B,GAAW,EAAE,GAAG,IAAI,CAAC;MAE7C,eAAe,CAAA;AAA5B,IAAA,WAAA,GAAA;AAyBY,QAAA,IAAA,CAAA,wBAAwB,GAC5B,IAAI,wBAAwB,CACxB,eAAe,CAAC,0BAA0B,EAC1C,eAAe,CAAC,0BAA0B,EAC1C,eAAe,CAAC,4BAA4B,CAC/C,CAAC;KAiET;AA9FG;;;AAGG;AACH,IAAA,WAAW,0BAA0B,GAAA;AACjC,QAAA,OAAO,0BAA0B,CAAC;KACrC;AACD,IAAA,WAAW,0BAA0B,GAAA;AACjC,QAAA,OAAO,0BAA0B,CAAC;KACrC;AACD,IAAA,WAAW,4BAA4B,GAAA;AACnC,QAAA,OAAO,4BAA4B,CAAC;KACvC;AACD,IAAA,WAAW,+BAA+B,GAAA;AACtC,QAAA,OAAO,+BAA+B,CAAC;KAC1C;IAGD,IAAI,YAAY,CAAC,KAAc,EAAA;AAC3B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;KAC9B;AAWD;;;;;;;AAOG;AACH,IAAA,MAAM,aAAa,CACf,cAAsB,EACtB,YAAoB,EACpB,MAAc,EAAA;QAEd,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;;AAG3B,YAAA,IAAI,CAAC,UAAU;gBACX,cAAc,KAAK,UAAU,CAAC,IAAI;AAC9B,sBAAE,2BAA2B;sBAC3B,gCAAgC,CAAC;AAC9C,SAAA;AAED;;;;;;AAMG;AACH,QAAA,IACI,CAAC,8CAA8C,CAAC,QAAQ,CACpD,cAAc,CACjB;AACG,aAAC,cAAc,IAAI,UAAU,CAAC,wBAAwB;gBAClD,cAAc,IAAI,UAAU,CAAC,sBAAsB;AACnD,gBAAA,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC;AACvC,YAAA,YAAY,GAAG,IAAI,CAAC,UAAU,EAChC;AACE,YAAA,MAAM,eAAe,GACjB,cAAc,KAAK,UAAU,CAAC,IAAI;kBAC5B,eAAe,CAAC,+BAA+B;kBAC/C,IAAI,CAAC,wBAAwB,CAAC,cAAc,CACxC,YAAY,CACf,CAAC;YAEZ,MAAM,CAAC,OAAO,CACV,CAAuB,oBAAA,EAAA,eAAe,CAClC,mBAAA,EAAA,YAAY,GAAG,CACnB,CAAG,CAAA,CAAA,CACN,CAAC;;AAGF,YAAA,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;AAC1B,gBAAA,OAAO,UAAU,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;AAChD,aAAC,CAAC,CAAC;AAEH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;;AAGD,QAAA,OAAO,KAAK,CAAC;KAChB;AACJ;;;;"}