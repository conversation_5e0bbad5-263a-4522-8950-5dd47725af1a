/**
 * Tenant Discovery Response which contains the relevant OAuth endpoints and data needed for authentication and authorization.
 */
export type OpenIdConfigResponse = {
    authorization_endpoint: string;
    token_endpoint: string;
    end_session_endpoint?: string;
    issuer: string;
    jwks_uri: string;
};
export declare function isOpenIdConfigResponse(response: object): boolean;
//# sourceMappingURL=OpenIdConfigResponse.d.ts.map