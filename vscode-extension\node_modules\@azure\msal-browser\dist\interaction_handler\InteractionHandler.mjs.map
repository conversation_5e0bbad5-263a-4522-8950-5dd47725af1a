{"version": 3, "file": "InteractionHandler.mjs", "sources": ["../../src/interaction_handler/InteractionHandler.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.userCancelled"], "mappings": ";;;;;;AAAA;;;AAGG;AAyBH;;AAEG;MACU,kBAAkB,CAAA;IAO3B,WACI,CAAA,cAAuC,EACvC,WAAgC,EAChC,eAA+C,EAC/C,MAAc,EACd,iBAAqC,EAAA;AAErC,QAAA,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC;AACjC,QAAA,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;AAClC,QAAA,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;AACvC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;AAED;;;AAGG;AACH,IAAA,MAAM,kBAAkB,CACpB,QAA2B,EAC3B,OAAsC,EAAA;AAEtC,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACtC,iBAAiB,CAAC,kBAAkB,EACpC,OAAO,CAAC,aAAa,CACxB,CAAC;AAEF,QAAA,IAAI,gBAAgB,CAAC;QACrB,IAAI;YACA,gBAAgB,GAAG,iBAAiB,CAAC,2BAA2B,CAC5D,QAAQ,EACR,OAAO,CAAC,KAAK,CAChB,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IACI,CAAC,YAAY,WAAW;AACxB,gBAAA,CAAC,CAAC,QAAQ,KAAKA,aAAmC,EACpD;;AAEE,gBAAA,MAAM,sBAAsB,CACxBA,aAAmC,CACtC,CAAC;AACL,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,CAAC,CAAC;AACX,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,WAAW,CACd,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC5C,iBAAiB,CAAC,4BAA4B,EAC9C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;KAChC;AAED;;;;;;;AAOG;IACH,MAAM,4BAA4B,CAC9B,gBAA0C,EAC1C,OAAsC,EACtC,gBAAyB,IAAI,EAAA;AAE7B,QAAA,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CACtC,iBAAiB,CAAC,4BAA4B,EAC9C,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,wDAAwD,CAC3D,CAAC;;QAGF,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC;;QAGlD,IAAI,gBAAgB,CAAC,wBAAwB,EAAE;AAC3C,YAAA,MAAM,WAAW,CACb,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EACrD,iBAAiB,CAAC,4BAA4B,EAC9C,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACvE,SAAA;;AAGD,QAAA,IAAI,aAAa,EAAE;;YAEf,gBAAgB,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,SAAS,CAAC;AACvD,SAAA;AAED,QAAA,gBAAgB,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;;QAGvC,IAAI,gBAAgB,CAAC,WAAW,EAAE;YAC9B,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,gBAAgB,CAAC,WAAW,CAAC;AAClE,SAAA;AAAM,aAAA;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;AACnD,YAAA,IAAI,OAAO,EAAE;AACT,gBAAA,IAAI,CAAC,eAAe,CAAC,aAAa,GAAG,OAAO,CAAC;AAChD,aAAA;AACJ,SAAA;;AAGD,QAAA,MAAM,aAAa,IAAI,MAAM,WAAW,CACpC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAClD,iBAAiB,CAAC,sBAAsB,EACxC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,aAAa,CACxB,CAAC,IAAI,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAyB,CAAC;AACnE,QAAA,OAAO,aAAa,CAAC;KACxB;AAED;;AAEG;AACO,IAAA,oBAAoB,CAC1B,OAAsC,EAAA;QAEtC,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,OAAO;AACH,gBAAA,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,aAAa;gBACzC,IAAI,EAAE,iBAAiB,CAAC,eAAe;aAC1C,CAAC;AACL,SAAA;aAAM,IAAI,OAAO,CAAC,SAAS,EAAE;YAC1B,OAAO;gBACH,UAAU,EAAE,OAAO,CAAC,SAAS;gBAC7B,IAAI,EAAE,iBAAiB,CAAC,GAAG;aAC9B,CAAC;AACL,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AACJ;;;;"}