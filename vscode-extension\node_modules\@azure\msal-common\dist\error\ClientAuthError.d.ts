import { AuthError } from "./AuthError.js";
import * as ClientAuthErrorCodes from "./ClientAuthErrorCodes.js";
export { ClientAuthErrorCodes };
/**
 * ClientAuthErrorMessage class containing string constants used by error codes and messages.
 */
export declare const ClientAuthErrorMessages: {
    client_info_decoding_error: string;
    client_info_empty_error: string;
    token_parsing_error: string;
    null_or_empty_token: string;
    endpoints_resolution_error: string;
    network_error: string;
    openid_config_error: string;
    hash_not_deserialized: string;
    invalid_state: string;
    state_mismatch: string;
    state_not_found: string;
    nonce_mismatch: string;
    auth_time_not_found: string;
    max_age_transpired: string;
    multiple_matching_tokens: string;
    multiple_matching_accounts: string;
    multiple_matching_appMetadata: string;
    request_cannot_be_made: string;
    cannot_remove_empty_scope: string;
    cannot_append_scopeset: string;
    empty_input_scopeset: string;
    device_code_polling_cancelled: string;
    device_code_expired: string;
    device_code_unknown_error: string;
    no_account_in_silent_request: string;
    invalid_cache_record: string;
    invalid_cache_environment: string;
    no_account_found: string;
    no_crypto_object: string;
    unexpected_credential_type: string;
    invalid_assertion: string;
    invalid_client_credential: string;
    token_refresh_required: string;
    user_timeout_reached: string;
    token_claims_cnf_required_for_signedjwt: string;
    authorization_code_missing_from_server_response: string;
    binding_key_not_removed: string;
    end_session_endpoint_not_supported: string;
    key_id_missing: string;
    no_network_connectivity: string;
    user_canceled: string;
    missing_tenant_id_error: string;
    method_not_implemented: string;
    nested_app_auth_bridge_disabled: string;
};
/**
 * String constants used by error codes and messages.
 * @deprecated Use ClientAuthErrorCodes instead
 */
export declare const ClientAuthErrorMessage: {
    clientInfoDecodingError: {
        code: string;
        desc: string;
    };
    clientInfoEmptyError: {
        code: string;
        desc: string;
    };
    tokenParsingError: {
        code: string;
        desc: string;
    };
    nullOrEmptyToken: {
        code: string;
        desc: string;
    };
    endpointResolutionError: {
        code: string;
        desc: string;
    };
    networkError: {
        code: string;
        desc: string;
    };
    unableToGetOpenidConfigError: {
        code: string;
        desc: string;
    };
    hashNotDeserialized: {
        code: string;
        desc: string;
    };
    invalidStateError: {
        code: string;
        desc: string;
    };
    stateMismatchError: {
        code: string;
        desc: string;
    };
    stateNotFoundError: {
        code: string;
        desc: string;
    };
    nonceMismatchError: {
        code: string;
        desc: string;
    };
    authTimeNotFoundError: {
        code: string;
        desc: string;
    };
    maxAgeTranspired: {
        code: string;
        desc: string;
    };
    multipleMatchingTokens: {
        code: string;
        desc: string;
    };
    multipleMatchingAccounts: {
        code: string;
        desc: string;
    };
    multipleMatchingAppMetadata: {
        code: string;
        desc: string;
    };
    tokenRequestCannotBeMade: {
        code: string;
        desc: string;
    };
    removeEmptyScopeError: {
        code: string;
        desc: string;
    };
    appendScopeSetError: {
        code: string;
        desc: string;
    };
    emptyInputScopeSetError: {
        code: string;
        desc: string;
    };
    DeviceCodePollingCancelled: {
        code: string;
        desc: string;
    };
    DeviceCodeExpired: {
        code: string;
        desc: string;
    };
    DeviceCodeUnknownError: {
        code: string;
        desc: string;
    };
    NoAccountInSilentRequest: {
        code: string;
        desc: string;
    };
    invalidCacheRecord: {
        code: string;
        desc: string;
    };
    invalidCacheEnvironment: {
        code: string;
        desc: string;
    };
    noAccountFound: {
        code: string;
        desc: string;
    };
    noCryptoObj: {
        code: string;
        desc: string;
    };
    unexpectedCredentialType: {
        code: string;
        desc: string;
    };
    invalidAssertion: {
        code: string;
        desc: string;
    };
    invalidClientCredential: {
        code: string;
        desc: string;
    };
    tokenRefreshRequired: {
        code: string;
        desc: string;
    };
    userTimeoutReached: {
        code: string;
        desc: string;
    };
    tokenClaimsRequired: {
        code: string;
        desc: string;
    };
    noAuthorizationCodeFromServer: {
        code: string;
        desc: string;
    };
    bindingKeyNotRemovedError: {
        code: string;
        desc: string;
    };
    logoutNotSupported: {
        code: string;
        desc: string;
    };
    keyIdMissing: {
        code: string;
        desc: string;
    };
    noNetworkConnectivity: {
        code: string;
        desc: string;
    };
    userCanceledError: {
        code: string;
        desc: string;
    };
    missingTenantIdError: {
        code: string;
        desc: string;
    };
    nestedAppAuthBridgeDisabled: {
        code: string;
        desc: string;
    };
};
/**
 * Error thrown when there is an error in the client code running on the browser.
 */
export declare class ClientAuthError extends AuthError {
    constructor(errorCode: string, additionalMessage?: string);
}
export declare function createClientAuthError(errorCode: string, additionalMessage?: string): ClientAuthError;
//# sourceMappingURL=ClientAuthError.d.ts.map