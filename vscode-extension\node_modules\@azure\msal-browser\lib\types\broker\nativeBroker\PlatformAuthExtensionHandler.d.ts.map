{"version": 3, "file": "PlatformAuthExtensionHandler.d.ts", "sourceRoot": "", "sources": ["../../../../src/broker/nativeBroker/PlatformAuthExtensionHandler.ts"], "names": [], "mappings": "AASA,OAAO,EACH,MAAM,EAMN,kBAAkB,EACrB,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAGH,mBAAmB,EACtB,MAAM,0BAA0B,CAAC;AAOlC,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AACjE,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AASjE,qBAAa,4BAA6B,YAAW,oBAAoB;IACrE,OAAO,CAAC,WAAW,CAAqB;IACxC,OAAO,CAAC,gBAAgB,CAAqB;IAC7C,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAS;IAC5C,OAAO,CAAC,SAAS,CAAqB;IACtC,OAAO,CAAC,SAAS,CAAyC;IAC1D,OAAO,CAAC,kBAAkB,CAAuC;IACjE,OAAO,CAAC,cAAc,CAAiB;IACvC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAgC;IAC/D,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAqB;IACvD,OAAO,CAAC,QAAQ,CAAC,cAAc,CAA6B;IAC5D,gBAAgB,EAAE,MAAM,CAAC;gBAGrB,MAAM,EAAE,MAAM,EACd,kBAAkB,EAAE,MAAM,EAC1B,iBAAiB,EAAE,kBAAkB,EACrC,WAAW,CAAC,EAAE,MAAM;IAiBxB;;;OAGG;IACG,WAAW,CACb,OAAO,EAAE,mBAAmB,GAC7B,OAAO,CAAC,oBAAoB,CAAC;IAqChC;;;;;;OAMG;WACU,cAAc,CACvB,MAAM,EAAE,MAAM,EACd,kBAAkB,EAAE,MAAM,EAC1B,iBAAiB,EAAE,kBAAkB,GACtC,OAAO,CAAC,4BAA4B,CAAC;IAwBxC;;OAEG;YACW,oBAAoB;IAsDlC;;;OAGG;IACH,OAAO,CAAC,eAAe;IA0DvB;;;OAGG;IACH,OAAO,CAAC,gBAAgB;IAuGxB;;;OAGG;IACH,OAAO,CAAC,8BAA8B;IAoBtC;;;OAGG;IACH,cAAc,IAAI,MAAM,GAAG,SAAS;IAIpC;;;OAGG;IACH,mBAAmB,IAAI,MAAM,GAAG,SAAS;IAIzC,gBAAgB,IAAI,MAAM,GAAG,SAAS;CAQzC"}