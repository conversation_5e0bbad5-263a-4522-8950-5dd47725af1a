{"version": 3, "file": "ManagedIdentityApplication.mjs", "sources": ["../../src/client/ManagedIdentityApplication.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;AAGG;AAsCH,MAAM,qCAAqC,GACvC,CAAC,0BAA0B,CAAC,cAAc,CAAC,CAAC;AAEhD;;;AAGG;MACU,0BAA0B,CAAA;AAkBnC,IAAA,WAAA,CAAY,aAA4C,EAAA;;QAEpD,IAAI,CAAC,MAAM,GAAG,iCAAiC,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;AAErE,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CACpB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAChC,IAAI,EACJ,OAAO,CACV,CAAC;AAEF,QAAA,MAAM,0BAA0B,GAA2B;YACvD,kBAAkB,EAAE,SAAS,CAAC,iBAAiB;SAClD,CAAC;AAEF,QAAA,IAAI,CAAC,0BAA0B,CAAC,WAAW,EAAE;YACzC,0BAA0B,CAAC,WAAW,GAAG,IAAI,WAAW,CACpD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE,EAChC,6BAA6B,EAC7B,0BAA0B,CAC7B,CAAC;AACL,SAAA;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;AAEtD,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;AAE3C,QAAA,MAAM,oBAAoB,GAAqB;YAC3C,YAAY,EAAE,YAAY,CAAC,GAAG;YAC9B,gBAAgB,EAAE,CAAC,sCAAsC,CAAC;AAC1D,YAAA,sBAAsB,EAAE,EAAE;AAC1B,YAAA,iBAAiB,EAAE,EAAE;SACxB,CAAC;AACF,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,SAAS,CAC9B,sCAAsC,EACtC,IAAI,CAAC,aAAa,EAClB,0BAA0B,CAAC,WAA0B,EACrD,oBAAoB,EACpB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;QACnC,SAAS,EACT,IAAI,CACP,CAAC;AAEF,QAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI,sBAAsB,CAAC;AACzD,YAAA,WAAW,EAAE;AACT,gBAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE;gBAC1C,SAAS,EAAE,IAAI,CAAC,aAAa;AACjB,aAAA;AACI,SAAA,CAAC,CAAC;AAE1B,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,qBAAqB,CAClD,IAAI,CAAC,MAAM,EACX,0BAA0B,CAAC,WAA0B,EACrD,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,MAAM,CAAC,sBAAsB,CACrC,CAAC;AAEF,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;KACpC;AAED;;;;AAIG;IACI,MAAM,YAAY,CACrB,4BAA0D,EAAA;AAE1D,QAAA,IAAI,CAAC,4BAA4B,CAAC,QAAQ,EAAE;AACxC,YAAA,MAAM,8BAA8B,CAChC,6BAA6B,CAAC,aAAa,CAC9C,CAAC;AACL,SAAA;AAED,QAAA,MAAM,sBAAsB,GAA2B;YACnD,YAAY,EAAE,4BAA4B,CAAC,YAAY;YACvD,QAAQ,EAAE,4BAA4B,CAAC,QAAQ,CAAC,OAAO,CACnD,WAAW,EACX,EAAE,CACL;AACD,YAAA,MAAM,EAAE;gBACJ,4BAA4B,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;AACjE,aAAA;AACD,YAAA,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,kBAAkB;AAChD,YAAA,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;YAClD,MAAM,EAAE,4BAA4B,CAAC,MAAM;AAC3C,YAAA,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB;SACrD,CAAC;QAEF,IAAI,sBAAsB,CAAC,YAAY,EAAE;AACrC,YAAA,OAAO,IAAI,CAAC,+BAA+B,CACvC,sBAAsB,EACtB,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,aAAa,CACrB,CAAC;AACL,SAAA;AAED,QAAA,MAAM,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,GAChD,MAAM,IAAI,CAAC,0BAA0B,CAAC,6BAA6B,CAC/D,sBAAsB,EACtB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,EAClB,0BAA0B,CAAC,WAA0B,CACxD,CAAC;AAEN;;;AAGG;QACH,IAAI,sBAAsB,CAAC,MAAM,EAAE;YAC/B,MAAM,UAAU,GACZ,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,EAAE,CAAC;AAE1D;;;AAGG;AACH,YAAA,IACI,0BAA0B;AAC1B,gBAAA,qCAAqC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAC5D;AACE,gBAAA,MAAM,sBAAsB,GAAW,IAAI,CAAC,SAAS;AAChD,qBAAA,MAAM,CAAC,0BAA0B,CAAC,WAAW,CAAC;AAC9C,qBAAA,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AACjC,gBAAA,sBAAsB,CAAC,sBAAsB;AACzC,oBAAA,sBAAsB,CAAC;AAC9B,aAAA;AAED,YAAA,OAAO,IAAI,CAAC,+BAA+B,CACvC,sBAAsB,EACtB,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,aAAa,CACrB,CAAC;AACL,SAAA;AAED,QAAA,IAAI,0BAA0B,EAAE;;AAE5B,YAAA,IAAI,gBAAgB,KAAK,YAAY,CAAC,qBAAqB,EAAE;AACzD,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,8JAA8J,CACjK,CAAC;;gBAGF,MAAM,kBAAkB,GAAG,IAAI,CAAC;AAChC,gBAAA,MAAM,IAAI,CAAC,+BAA+B,CACtC,sBAAsB,EACtB,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,aAAa,EAClB,kBAAkB,CACrB,CAAC;AACL,aAAA;AAED,YAAA,OAAO,0BAA0B,CAAC;AACrC,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC,+BAA+B,CACvC,sBAAsB,EACtB,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,aAAa,CACrB,CAAC;AACL,SAAA;KACJ;AAED;;;;;;;;AAQG;IACK,MAAM,+BAA+B,CACzC,sBAA8C,EAC9C,iBAAoC,EACpC,aAAwB,EACxB,kBAA4B,EAAA;;AAG5B,QAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,+BAA+B,CAC7D,sBAAsB,EACtB,iBAAiB,EACjB,aAAa,EACb,kBAAkB,CACrB,CAAC;KACL;AAED;;;AAGG;IACI,wBAAwB,GAAA;QAC3B,QACI,qBAAqB,CAAC,UAAU;AAChC,YAAA,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,EAAE,EACvD;KACL;AACJ;;;;"}